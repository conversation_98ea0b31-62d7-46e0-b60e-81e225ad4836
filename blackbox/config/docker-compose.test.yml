# 🧪 MCP Server ODI - Blackbox Test Environment
# Docker Compose configuration for comprehensive testing with NACOS integration
#
# Usage:
#   make test-fast    # Quick test
#   make test-full    # Complete test suite
#   make clean        # Cleanup
#
# Environment Configuration:
#   - Copy blackbox/config/.env.test to .env.test.local
#   - Update .env.test.local with actual test credentials
#   - Docker Compose will load environment variables automatically

version: '3.8'

services:
  # OAuth2 disabled server
  mcp-server-oauth2-disabled:
    build:
      context: ../..
      dockerfile: Dockerfile.test
      target: production
    environment:
      # Basic Configuration
      - NODE_ENV=test
      - TRANSPORT=http
      - MCP_SERVER_PORT=3000
      - OAUTH2_ENABLED=false
      - CONNECTION_AUTH_ENABLED=false
      - AUTH_COOKIES=test_orders-portal=ZjBhN2QwZTktMmU0ZS00NjhkLWJmNjUtOWNmNzlmNDk5ODVkQDE3NTQ1NzMzMTU5NzguM2QyZTI2NWMtZjg5NS00ZTMyLWIzYmUtNWExMzlmYTg1NmMxLjM2MDA=

      # NACOS Configuration (Test Environment)
      - MSE_ACCESS_KEY=${MSE_ACCESS_KEY_TEST:-test-access-key}
      - MSE_ACCESS_SECRET=${MSE_ACCESS_SECRET_TEST:-test-access-secret}
      - NACOS_SERVER_ADDR=${NACOS_SERVER_ADDR_TEST:-mse-9e4abfa0-nacos-ans.mse.aliyuncs.com:8848}
      - NACOS_NAMESPACE=${NACOS_NAMESPACE_TEST:-8bc453ff-d8df-4ee3-acf4-4de86c19f955}
      - NACOS_ACCESS_KEY=${MSE_ACCESS_KEY_TEST:-test-access-key}
      - NACOS_SECRET_KEY=${MSE_ACCESS_SECRET_TEST:-test-access-secret}

      # Test API Endpoints
      - VITE_API_HOST=${VITE_API_HOST_TEST:-https://fe-dev-i.ingka-dt.cn}
      - VITE_API_HOST_KONG=${VITE_API_HOST_KONG_TEST:-https://api-dev-mpp-fe.ingka-dt.cn}
      - VITE_MASTER_DATA_API_HOST=${VITE_MASTER_DATA_API_HOST_TEST:-https://master-data-api-dev.ingka-dt.cn}
      - VITE_MASTER_DATA_API_KEY=${VITE_MASTER_DATA_API_KEY_TEST:-test-api-key}
      - X_CUSTOM_REFERRER=${X_CUSTOM_REFERRER_TEST:-https://fe-dev-i.ingka-dt.cn/order-web}

      # Debug Configuration
      - DEBUG_SERVICE_ADAPTER=true
    ports:
      - "3000:3000"
    command: ["node", "dist/index.js"]

  # OAuth2 enabled server
  mcp-server-oauth2-enabled:
    build:
      context: ../..
      dockerfile: Dockerfile
      target: production
    environment:
      # Basic Configuration
      - NODE_ENV=test
      - TRANSPORT=http
      - MCP_SERVER_PORT=3001
      - OAUTH2_ENABLED=true
      - CONNECTION_AUTH_ENABLED=true
      - AUTH_COOKIES=test_orders-portal=ZjBhN2QwZTktMmU0ZS00NjhkLWJmNjUtOWNmNzlmNDk5ODVkQDE3NTQ1NzMzMTU5NzguM2QyZTI2NWMtZjg5NS00ZTMyLWIzYmUtNWExMzlmYTg1NmMxLjM2MDA=

      # OAuth2 Configuration
      - OAUTH2_CLIENT_SECRET=test-secret-for-testing
      - KEYCLOAK_BASE_URL=https://keycloak.ingka-dt.cn
      - KEYCLOAK_REALM=master
      - OAUTH2_CLIENT_ID=mcp-server
      - OAUTH2_REDIRECT_URI=http://localhost:3001/auth/callback
      - OAUTH2_SCOPES=openid,profile,email

      # NACOS Configuration (Test Environment)
      - MSE_ACCESS_KEY=${MSE_ACCESS_KEY_TEST:-test-access-key}
      - MSE_ACCESS_SECRET=${MSE_ACCESS_SECRET_TEST:-test-access-secret}
      - NACOS_SERVER_ADDR=${NACOS_SERVER_ADDR_TEST:-mse-9e4abfa0-nacos-ans.mse.aliyuncs.com:8848}
      - NACOS_NAMESPACE=${NACOS_NAMESPACE_TEST:-8bc453ff-d8df-4ee3-acf4-4de86c19f955}
      - NACOS_ACCESS_KEY=${MSE_ACCESS_KEY_TEST:-test-access-key}
      - NACOS_SECRET_KEY=${MSE_ACCESS_SECRET_TEST:-test-access-secret}

      # Test API Endpoints
      - VITE_API_HOST=${VITE_API_HOST_TEST:-https://fe-dev-i.ingka-dt.cn}
      - VITE_API_HOST_KONG=${VITE_API_HOST_KONG_TEST:-https://api-dev-mpp-fe.ingka-dt.cn}
      - VITE_MASTER_DATA_API_HOST=${VITE_MASTER_DATA_API_HOST_TEST:-https://master-data-api-dev.ingka-dt.cn}
      - VITE_MASTER_DATA_API_KEY=${VITE_MASTER_DATA_API_KEY_TEST:-test-api-key}
      - X_CUSTOM_REFERRER=${X_CUSTOM_REFERRER_TEST:-https://fe-dev-i.ingka-dt.cn/order-web}

      # Debug Configuration
      - DEBUG_SERVICE_ADAPTER=true
    ports:
      - "3001:3001"
    command: ["node", "dist/index.js"]

  # Test runner
  test-runner:
    build:
      context: ../..
      dockerfile: Dockerfile
      target: production
    environment:
      # Test Runner Configuration
      - NODE_ENV=test
      - MCP_SERVER_OAUTH2_DISABLED_URL=http://mcp-server-oauth2-disabled:3000
      - MCP_SERVER_OAUTH2_ENABLED_URL=http://mcp-server-oauth2-enabled:3001

      # NACOS Configuration for Test Runner (if needed for config validation)
      - MSE_ACCESS_KEY=${MSE_ACCESS_KEY_TEST:-test-access-key}
      - MSE_ACCESS_SECRET=${MSE_ACCESS_SECRET_TEST:-test-access-secret}
      - NACOS_SERVER_ADDR=${NACOS_SERVER_ADDR_TEST:-mse-9e4abfa0-nacos-ans.mse.aliyuncs.com:8848}
      - NACOS_NAMESPACE=${NACOS_NAMESPACE_TEST:-8bc453ff-d8df-4ee3-acf4-4de86c19f955}
    volumes:
      - ./test:/app/test:ro
      - ../results:/app/blackbox/results
    depends_on:
      - mcp-server-oauth2-disabled
      - mcp-server-oauth2-enabled
    command: ["node", "blackbox/tests/docker-test-runner.js"]
