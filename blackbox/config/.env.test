# ================================
# 🧪 MCP Server ODI - Blackbox Test Environment Configuration
# ================================
#
# ⚠️  SECURITY NOTICE:
# 1. This file contains test-specific environment variables
# 2. Copy this file to .env.test.local for local overrides: cp .env.test .env.test.local
# 3. Update .env.test.local with your actual test credentials
# 4. Never commit .env.test.local to version control
#
# 📋 Configuration for blackbox testing with NACOS integration
# ================================

# ================================
# 🌍 Test Environment Configuration
# ================================
NODE_ENV=test

# ================================
# 🔗 NACOS/MSE Configuration (Test Environment)
# ================================
# Test MSE access credentials (replace with actual test credentials)
MSE_ACCESS_KEY_TEST=test-access-key-replace-with-actual
MSE_ACCESS_SECRET_TEST=test-access-secret-replace-with-actual

# NACOS server configuration (test environment)
NACOS_SERVER_ADDR_TEST=mse-9e4abfa0-nacos-ans.mse.aliyuncs.com:8848
NACOS_NAMESPACE_TEST=8bc453ff-d8df-4ee3-acf4-4de86c19f955

# NACOS Configuration Testing
NACOS_CONFIG_ENABLED_TEST=true
NACOS_CONFIG_TIMEOUT_TEST=5000

# ================================
# 🚀 Test API Endpoints
# ================================
# Test environment API endpoints
VITE_API_HOST_TEST=https://fe-dev-i.ingka-dt.cn
VITE_API_HOST_KONG_TEST=https://api-dev-mpp-fe.ingka-dt.cn
VITE_MASTER_DATA_API_HOST_TEST=https://master-data-api-dev.ingka-dt.cn
VITE_MASTER_DATA_API_KEY_TEST=test-api-key-replace-with-actual
X_CUSTOM_REFERRER_TEST=https://fe-dev-i.ingka-dt.cn/order-web

# ================================
# 🔐 Test Authentication
# ================================
# Test authentication cookies (for blackbox testing)
AUTH_COOKIES_TEST=test_orders-portal=ZjBhN2QwZTktMmU0ZS00NjhkLWJmNjUtOWNmNzlmNDk5ODVkQDE3NTQ1NzMzMTU5NzguM2QyZTI2NWMtZjg5NS00ZTMyLWIzYmUtNWExMzlmYTg1NmMxLjM2MDA=

# OAuth2 test configuration
OAUTH2_CLIENT_SECRET_TEST=test-secret-for-testing
KEYCLOAK_BASE_URL_TEST=https://keycloak.ingka-dt.cn
KEYCLOAK_REALM_TEST=master
OAUTH2_CLIENT_ID_TEST=mcp-server

# ================================
# 🐳 Docker Test Configuration
# ================================
# Test server ports
MCP_SERVER_PORT_OAUTH2_DISABLED=3000
MCP_SERVER_PORT_OAUTH2_ENABLED=3001

# ================================
# 📝 Usage Instructions
# ================================
# 1. Copy this file: cp blackbox/config/.env.test blackbox/config/.env.test.local
# 2. Update .env.test.local with actual test credentials
# 3. Run tests: make test-fast or make test-full
# 4. The Docker Compose will automatically load these variables
#
# For CI/CD environments, set these as environment variables or secrets
