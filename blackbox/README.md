# 🧪 MCP Server ODI - Blackbox Testing Suite

A comprehensive blackbox testing infrastructure for the MCP (Model Context Protocol) Server ODI, providing thorough end-to-end testing with Docker-based isolation, automated reporting, and failure analysis.

## 📁 Directory Structure

```
blackbox/
├── README.md                    # This file - comprehensive testing guide
├── config/                      # Test configuration files
│   └── docker-compose.test.yml  # Docker Compose test environment
├── tests/                       # All test implementations
│   ├── docker-test-runner.js    # Main test orchestrator
│   ├── mcp-client-simulator.js  # MCP protocol client simulation
│   ├── protocol-compliance-test.js  # MCP protocol compliance tests
│   ├── edge-cases-test.js       # Edge case and error handling tests
│   ├── load-test.js             # Performance and load testing
│   ├── security-test.js         # Security and authentication tests
│   ├── all-tools.js             # MCP tools functionality tests
│   ├── quick-auth-test.js       # Quick authentication validation
│   ├── streamable-http-auth-suite.js  # HTTP auth suite
│   ├── benchmark-runner.js      # Performance benchmarking
│   ├── coverage-report.js       # Test coverage analysis
│   ├── status-dashboard.js      # Real-time test monitoring
│   └── test.js                  # Basic unified test suite
├── results/                     # Test results and reports
│   ├── docker-test-report-*.json    # Detailed test reports
│   ├── failure-analysis-*.json     # Failure analysis reports
│   └── (generated test artifacts)
└── scripts/                     # Test utility scripts (future)
```

## 🚀 Quick Start

### Prerequisites
- Docker and Docker Compose installed
- Node.js 18+ with npm
- MCP Server ODI built and ready
- NACOS/MSE test credentials (for enterprise testing)

### Setup NACOS Test Environment

```bash
# Setup NACOS test configuration (first time)
make setup-nacos-test

# Validate NACOS test configuration
make validate-nacos-test

# Edit test credentials (required)
nano blackbox/config/.env.test.local
```

### Run Integration Tests

```bash
# Make commands (recommended)
make test-fast        # Quick blackbox test (~20s, for development)
make test-full        # Complete test suite (~2-5min, for CI/CD)
make clean           # Clean up Docker resources

# NACOS configuration management
make setup-nacos-test     # Setup NACOS test environment
make validate-nacos-test  # Validate NACOS test configuration

# Development utilities
make logs            # Show Docker service logs
make status          # Show Docker service status
make summary         # Show all available commands

# Convenience script (alternative)
./blackbox/scripts/run-tests.sh fast    # Quick test
./blackbox/scripts/run-tests.sh full    # Full test suite
./blackbox/scripts/run-tests.sh clean   # Clean up
```

## 🔧 NACOS Configuration Management

The blackbox tests now support NACOS/MSE integration for enterprise-grade configuration management, following the same pattern as production services.

### Configuration Files

- **`blackbox/config/.env.test`** - Template with default test values
- **`blackbox/config/.env.test.local`** - Local overrides with actual credentials (gitignored)
- **`blackbox/config/docker-compose.test.yml`** - Docker Compose with NACOS environment variables

### NACOS Environment Variables

```bash
# MSE/NACOS Access Credentials
MSE_ACCESS_KEY_TEST=your-test-access-key
MSE_ACCESS_SECRET_TEST=your-test-access-secret

# NACOS Server Configuration
NACOS_SERVER_ADDR_TEST=mse-9e4abfa0-nacos-ans.mse.aliyuncs.com:8848
NACOS_NAMESPACE_TEST=8bc453ff-d8df-4ee3-acf4-4de86c19f955

# Test API Endpoints
VITE_API_HOST_TEST=https://fe-dev-i.ingka-dt.cn
VITE_MASTER_DATA_API_KEY_TEST=your-test-api-key
```

### Setup Process

1. **Initial Setup**: `make setup-nacos-test`
2. **Edit Credentials**: Update `blackbox/config/.env.test.local` with actual values
3. **Validate**: `make validate-nacos-test`
4. **Run Tests**: `make test-fast` or `make test-full`

## 🧪 Test Categories

### 1. **Fast Test** (`make test-fast`)
- **Duration**: ~21 seconds
- **Coverage**: Core MCP functionality
- **Use Case**: Development, CI/CD, quick validation
- **Success Criteria**: 80%+ pass rate

### 2. **Full Test Suite** (`make test-full`)
- **Duration**: ~2-5 minutes
- **Coverage**: Complete blackbox testing
- **Use Case**: Release validation, comprehensive testing
- **Success Criteria**: 90%+ pass rate

### 3. **Unit Tests** (Future)
```bash
npm test                 # Show testing guidance
npm run test:unit        # Run unit tests (when implemented)
npm run test:lint        # Code quality checks (when implemented)
```

## 🏗️ Testing Architecture

### Blackbox Testing Approach
The testing suite follows a **complete blackbox methodology**:

1. **🐳 Docker Isolation**: Tests run in isolated containers
2. **🔌 External Interface Testing**: Only tests public APIs and endpoints
3. **📊 Behavior Validation**: Validates expected outputs for given inputs
4. **🚫 No Internal Dependencies**: No access to internal code or state
5. **🌐 Real Network Communication**: Uses actual HTTP/WebSocket protocols

### Test Flow
```mermaid
graph TD
    A[Start Test Suite] --> B[Build Docker Images]
    B --> C[Start Test Services]
    C --> D[Wait for Service Readiness]
    D --> E[Run Blackbox Tests]
    E --> F[Collect Results & Logs]
    F --> G[Generate Reports]
    G --> H[Cleanup Resources]
    H --> I[Exit with Status Code]
```

## 📊 Test Reporting

### Automated Reports
- **JSON Reports**: Detailed machine-readable results in `blackbox/results/`
- **Failure Analysis**: Automated issue detection and recommendations
- **Service Logs**: Complete service logs collected for debugging
- **Performance Metrics**: Response times, success rates, throughput

### Report Structure
```json
{
  "timestamp": "2024-01-15T10:30:00.000Z",
  "duration": 45,
  "summary": {
    "totalTests": 25,
    "totalPassed": 20,
    "successRate": 80,
    "suites": 5
  },
  "results": [...],
  "serviceLogs": {...},
  "failureAnalysis": {...}
}
```

## 🔧 Configuration

### Environment Variables
```bash
# Test target URLs
MCP_SERVER_URL=http://localhost:3000                    # OAuth2 disabled server
MCP_SERVER_OAUTH2_DISABLED_URL=http://localhost:3000    # OAuth2 disabled server
MCP_SERVER_OAUTH2_ENABLED_URL=http://localhost:3001     # OAuth2 enabled server

# Test parameters
CONCURRENT_CLIENTS=10        # Load test concurrency
TEST_DURATION=30            # Load test duration (seconds)
NODE_ENV=test               # Environment mode
```

### Docker Services
- **mcp-server-oauth2-disabled** (port 3000): Standard MCP server
- **mcp-server-oauth2-enabled** (port 3001): OAuth2-enabled MCP server
- **test-runner**: Orchestrates all test execution

## 🎯 Test Scenarios

### MCP Protocol Compliance
- ✅ Initialize handshake
- ✅ Session management
- ✅ Tools listing and execution
- ✅ Error handling
- ✅ Protocol version compatibility

### Authentication & Security
- ✅ OAuth2 flow validation
- ✅ Session security
- ✅ Authorization checks
- ✅ Cookie handling
- ✅ CORS compliance

### Performance & Load
- ✅ Concurrent client handling
- ✅ Response time benchmarks
- ✅ Memory usage monitoring
- ✅ Connection pooling
- ✅ Rate limiting

### Edge Cases & Error Handling
- ✅ Invalid requests
- ✅ Malformed JSON
- ✅ Network timeouts
- ✅ Server errors
- ✅ Resource exhaustion

## 📈 Success Criteria

### Test Thresholds
- **Fast Test**: 80%+ success rate (development)
- **Full Test**: 90%+ success rate (production)
- **Load Test**: <500ms average response time
- **Security Test**: 100% authentication checks pass

### Quality Gates
- All critical MCP protocol tests must pass
- No security vulnerabilities detected
- Performance within acceptable limits
- Proper error handling for edge cases

## 🛠️ Troubleshooting

### Common Issues

#### Test Failures
```bash
# Check service logs
docker-compose -f blackbox/config/docker-compose.test.yml logs

# Review test reports
ls -la blackbox/results/

# Clean and retry
make clean && make test-fast
```

#### Docker Issues
```bash
# Reset Docker environment
docker system prune -f --volumes
docker-compose -f blackbox/config/docker-compose.test.yml down -v

# Rebuild images
docker-compose -f blackbox/config/docker-compose.test.yml build --no-cache
```

#### Port Conflicts
```bash
# Check port usage
lsof -i :3000 -i :3001

# Kill conflicting processes
pkill -f "node.*3000"
```

## 🔄 Continuous Integration

### GitHub Actions Integration
```yaml
name: Blackbox Tests
on: [push, pull_request]
jobs:
  blackbox-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
      - run: npm ci
      - run: make test-full
      - uses: actions/upload-artifact@v3
        with:
          name: test-reports
          path: blackbox/results/
```

### Local Development Workflow
1. Make code changes
2. Run `make test-fast` for quick validation
3. Run `make test-full` before committing
4. Review test reports in `blackbox/results/`
5. Address any failures before pushing

## 📚 Advanced Usage

### Custom Test Execution
```bash
# Run specific test with custom parameters
MCP_SERVER_URL=http://localhost:3000 CONCURRENT_CLIENTS=20 npm run test:load

# Run tests against external server
MCP_SERVER_URL=https://your-server.com npm run test:mcp-client

# Generate coverage report
make coverage-report
```

### Test Development
- Add new tests to `blackbox/tests/`
- Update `package.json` scripts
- Add Makefile targets for convenience
- Follow blackbox testing principles

## 🤝 Contributing

1. Follow blackbox testing methodology
2. Add comprehensive test coverage
3. Include proper error handling
4. Update documentation
5. Ensure tests are deterministic and reliable

---

**🎯 Goal**: Provide confidence in MCP Server ODI reliability through comprehensive blackbox testing that validates real-world usage scenarios without depending on internal implementation details.
