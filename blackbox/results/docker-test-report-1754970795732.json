{"timestamp": "2025-08-12T03:53:15.732Z", "duration": 6, "summary": {"totalTests": 10, "totalPassed": 8, "successRate": 80, "suites": 3}, "results": [{"suite": "OAuth2 Disabled Tests", "status": "PASS", "passed": 3, "total": 5, "details": [{"status": "fulfilled", "value": {"status": "healthy", "transport": "streamable-http", "timestamp": "2025-08-12T03:53:15.584Z", "activeSessions": 0, "sessions": []}}, {"status": "fulfilled", "value": {"transport": "streamable-http", "streaming": true, "protocols": ["jsonrpc-2.0"], "version": "1.0.0", "features": ["session-management", "server-sent-events"], "oauth2_enabled": false}}, {"status": "fulfilled", "value": {"status": "initialized"}}, {"status": "rejected", "reason": {}}, {"status": "rejected", "reason": {}}]}, {"suite": "OAuth2 Enabled Tests", "status": "PASS", "passed": 4, "total": 4, "details": [{"status": "fulfilled", "value": {"status": "healthy", "transport": "streamable-http", "timestamp": "2025-08-12T03:53:15.606Z", "activeSessions": 0, "sessions": []}}, {"status": "fulfilled", "value": {"transport": "streamable-http", "streaming": true, "protocols": ["jsonrpc-2.0"], "version": "1.0.0", "features": ["session-management", "server-sent-events"], "oauth2_enabled": true}}, {"status": "fulfilled", "value": {"status": 200}}, {"status": "fulfilled", "value": {"status": 401, "authRequired": true}}]}, {"suite": "MCP Client Simulation", "status": "PASS", "passed": 1, "total": 1, "output": "\u001b[1m\n🤖 MCP Client Simulator\u001b[0m\n\u001b[34m🌐 Server: http://mcp-server-oauth2-disabled:3000\u001b[0m\n============================================================\n\u001b[36m\n📡 Step 1: MCP Initialize\u001b[0m\n\u001b[34m   Session ID: bd504a85-f87b-4540-a238-87bf4d3ef62f\u001b[0m\n\u001b[32m✅ Initialize successful (31ms)\u001b[0m\n\u001b[34m   Protocol: 2024-11-05\u001b[0m\n\u001b[34m   Server: mcp-server-odi v0.1.0\u001b[0m\n\u001b[36m\n🔧 Step 2: List Tools\u001b[0m\n\u001b[32m✅ Tools listed successfully (4ms)\u001b[0m\n\u001b[34m   Found 9 tools\u001b[0m\n\u001b[34m   - oms_queryOrderLists: Query order lists from OMS system\u001b[0m\n\u001b[34m   - oms_getOrderDetail: Get detailed information for a specific order\u001b[0m\n\u001b[34m   - oms_getLogisticsInfo: Get logistics information for an order\u001b[0m\n\u001b[34m   ... and 6 more tools\u001b[0m\n\u001b[36m\n⚡ Step 3: Tool Execution\u001b[0m\n\u001b[32m✅ Tool execution successful (2ms)\u001b[0m\n\u001b[34m   Tool: test_ping\u001b[0m\n\u001b[34m   Result: {\"content\":[{\"type\":\"text\",\"text\":\"{\\n  \\\"success\\\": true,\\n  \\\"message\\\": \\\"pong\\\",\\n  \\\"timestamp\\...\u001b[0m\n\u001b[36m\n🚨 Step 4: Error Handling\u001b[0m\n\u001b[33m⚠️ Exp..."}], "environment": {"nodeEnv": "test", "oauth2DisabledUrl": "http://mcp-server-oauth2-disabled:3000", "oauth2EnabledUrl": "http://mcp-server-oauth2-enabled:3001"}, "serviceLogs": {"oauth2Disabled": "Log collection unavailable: spawn docker-compose ENOENT", "oauth2Enabled": "Log collection unavailable: spawn docker-compose ENOENT"}}