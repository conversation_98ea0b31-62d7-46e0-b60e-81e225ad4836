#!/usr/bin/env node

/**
 * 📊 Test Suite Performance Benchmark
 * 
 * Measures execution time and performance of all test categories
 * Provides recommendations for optimization
 */

import { spawn } from 'child_process';
import { performance } from 'perf_hooks';

const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  bold: '\x1b[1m'
};

function colorize(text, color) {
  return `${colors[color]}${text}${colors.reset}`;
}

class TestBenchmark {
  constructor() {
    this.results = [];
    this.serverStartTime = null;
  }

  async runBenchmark() {
    console.log(colorize('\n📊 MCP Server Test Suite Benchmark', 'bold'));
    console.log('='.repeat(60));

    // Start server for testing
    console.log(colorize('\n🚀 Starting test server...', 'cyan'));
    const serverStart = performance.now();
    
    try {
      await this.startServer();
      const serverTime = performance.now() - serverStart;
      console.log(colorize(`✅ Server started in ${serverTime.toFixed(0)}ms`, 'green'));

      // Run individual test benchmarks
      const tests = [
        { name: 'MCP Client Simulator', command: 'npm run test:mcp-client' },
        { name: 'Protocol Compliance', command: 'npm run test:protocol' },
        { name: 'Edge Cases', command: 'npm run test:edge-cases' },
        { name: 'Load Testing', command: 'npm run test:load' },
        { name: 'Security Testing', command: 'npm run test:security' },
        { name: 'All Tools', command: 'npm run test:all-tools' }
      ];

      for (const test of tests) {
        await this.benchmarkTest(test);
      }

      // Generate report
      this.generateReport();

    } finally {
      await this.stopServer();
    }
  }

  async startServer() {
    return new Promise((resolve, reject) => {
      const server = spawn('docker-compose', ['-f', 'docker-compose.test.yml', 'up', 'mcp-server-oauth2-disabled', '--detach'], {
        stdio: 'pipe'
      });

      server.on('close', (code) => {
        if (code === 0) {
          // Wait for server to be ready
          setTimeout(resolve, 5000);
        } else {
          reject(new Error(`Server failed to start: ${code}`));
        }
      });
    });
  }

  async stopServer() {
    return new Promise((resolve) => {
      const stop = spawn('docker-compose', ['-f', 'docker-compose.test.yml', 'down'], {
        stdio: 'pipe'
      });
      stop.on('close', () => resolve());
    });
  }

  async benchmarkTest(test) {
    console.log(colorize(`\n🧪 Benchmarking: ${test.name}`, 'yellow'));
    
    const start = performance.now();
    
    try {
      const result = await this.runCommand(test.command);
      const duration = performance.now() - start;
      
      const benchmark = {
        name: test.name,
        duration: Math.round(duration),
        success: result.code === 0,
        output: result.output
      };

      this.results.push(benchmark);
      
      const status = benchmark.success ? '✅' : '❌';
      console.log(`${status} ${test.name}: ${benchmark.duration}ms`);
      
    } catch (error) {
      const duration = performance.now() - start;
      this.results.push({
        name: test.name,
        duration: Math.round(duration),
        success: false,
        error: error.message
      });
      console.log(colorize(`❌ ${test.name}: ${Math.round(duration)}ms (${error.message})`, 'red'));
    }
  }

  async runCommand(command) {
    return new Promise((resolve, reject) => {
      const [cmd, ...args] = command.split(' ');
      const process = spawn(cmd, args, {
        stdio: 'pipe',
        env: { ...process.env, MCP_SERVER_URL: 'http://localhost:3000' }
      });

      let output = '';
      let errorOutput = '';

      process.stdout.on('data', (data) => {
        output += data.toString();
      });

      process.stderr.on('data', (data) => {
        errorOutput += data.toString();
      });

      process.on('close', (code) => {
        resolve({
          code,
          output: output || errorOutput
        });
      });

      process.on('error', (error) => {
        reject(error);
      });
    });
  }

  generateReport() {
    console.log(colorize('\n📊 BENCHMARK REPORT', 'bold'));
    console.log('='.repeat(60));

    const successful = this.results.filter(r => r.success);
    const failed = this.results.filter(r => !r.success);
    const totalTime = this.results.reduce((sum, r) => sum + r.duration, 0);
    const avgTime = Math.round(totalTime / this.results.length);

    console.log(colorize(`\n📈 Summary:`, 'cyan'));
    console.log(`   Total Tests: ${this.results.length}`);
    console.log(`   Successful: ${colorize(successful.length, 'green')}`);
    console.log(`   Failed: ${colorize(failed.length, failed.length > 0 ? 'red' : 'green')}`);
    console.log(`   Total Time: ${colorize(totalTime + 'ms', 'yellow')}`);
    console.log(`   Average Time: ${colorize(avgTime + 'ms', 'yellow')}`);

    console.log(colorize(`\n⚡ Performance Breakdown:`, 'cyan'));
    this.results
      .sort((a, b) => a.duration - b.duration)
      .forEach(result => {
        const status = result.success ? '✅' : '❌';
        const time = result.duration.toString().padStart(6);
        console.log(`   ${status} ${time}ms - ${result.name}`);
      });

    console.log(colorize(`\n💡 Recommendations:`, 'magenta'));
    
    if (successful.length === this.results.length) {
      console.log('   🎉 All tests passing! Great job!');
    }
    
    const slowTests = this.results.filter(r => r.duration > 10000);
    if (slowTests.length > 0) {
      console.log('   ⚠️  Consider optimizing slow tests (>10s):');
      slowTests.forEach(test => {
        console.log(`      - ${test.name}: ${test.duration}ms`);
      });
    }

    const fastestTest = this.results.reduce((min, r) => r.duration < min.duration ? r : min);
    const slowestTest = this.results.reduce((max, r) => r.duration > max.duration ? r : max);
    
    console.log(`   🚀 Fastest: ${fastestTest.name} (${fastestTest.duration}ms)`);
    console.log(`   🐌 Slowest: ${slowestTest.name} (${slowestTest.duration}ms)`);
    
    if (totalTime < 60000) {
      console.log('   ✅ Total execution time is excellent (<1 minute)');
    } else if (totalTime < 120000) {
      console.log('   ⚠️  Total execution time is acceptable (<2 minutes)');
    } else {
      console.log('   ❌ Consider optimizing - total time >2 minutes');
    }
  }
}

// Run benchmark
const benchmark = new TestBenchmark();
benchmark.runBenchmark().catch(console.error);
