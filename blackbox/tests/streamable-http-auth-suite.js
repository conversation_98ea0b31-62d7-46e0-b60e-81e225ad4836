#!/usr/bin/env node

/**
 * Comprehensive Test Suite for Streamable HTTP Authentication
 * 
 * Tests both OAuth2 enabled and disabled scenarios with streamable HTTP transport
 * 
 * Usage:
 *   node test/streamable-http-auth-suite.js
 *   npm run test:auth-suite
 */

import { spawn } from 'child_process';
import { readFileSync, writeFileSync, existsSync } from 'fs';
import { resolve } from 'path';

const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  bold: '\x1b[1m'
};

function colorize(text, color) {
  return `${colors[color]}${text}${colors.reset}`;
}

class TestSuite {
  constructor() {
    this.results = [];
    this.serverProcess = null;
    this.originalEnv = null;
    this.testPort = 6301; // Use different port to avoid conflicts
  }

  async run() {
    console.log(colorize('\n🧪 Streamable HTTP Authentication Test Suite', 'bold'));
    console.log('='.repeat(60));

    try {
      // Backup original .env
      await this.backupEnv();

      // Test 1: OAuth2 Disabled
      console.log(colorize('\n📋 Test Suite 1: OAuth2 Disabled + Streamable HTTP', 'cyan'));
      await this.testOAuth2Disabled();

      // Test 2: OAuth2 Enabled
      console.log(colorize('\n📋 Test Suite 2: OAuth2 Enabled + Streamable HTTP', 'cyan'));
      await this.testOAuth2Enabled();

      // Summary
      this.printSummary();

    } catch (error) {
      console.error(colorize(`❌ Test suite failed: ${error.message}`, 'red'));
    } finally {
      // Cleanup
      await this.cleanup();
    }
  }

  async backupEnv() {
    const envPath = resolve(process.cwd(), '.env');
    if (existsSync(envPath)) {
      this.originalEnv = readFileSync(envPath, 'utf8');
      console.log(colorize('💾 Backed up original .env file', 'blue'));
    }
  }

  async restoreEnv() {
    if (this.originalEnv) {
      const envPath = resolve(process.cwd(), '.env');
      writeFileSync(envPath, this.originalEnv);
      console.log(colorize('🔄 Restored original .env file', 'blue'));
    }
  }

  async updateEnvForTest(config) {
    const envPath = resolve(process.cwd(), '.env');
    const baseConfig = `# Test Configuration - Auto Generated
NODE_ENV=test
TRANSPORT=http
MCP_SERVER_PORT=${this.testPort}

# API Endpoints
VITE_API_HOST=https://admin.ingka-dt.cn/app-api/orders-portal/uat
VITE_API_HOST_KONG=https://fe-dev-i.ingka-dt.cn/order-web
VITE_MASTER_DATA_API_HOST=https://admin.ingka-dt.cn/master-data
VITE_MASTER_DATA_API_KEY=test-api-key

# Authentication
AUTH_COOKIES=test_orders-portal=test-cookie-value
X_CUSTOM_REFERRER=https://admin.ingka-dt.cn/app/orders-portal/oms/index

# OAuth2 Configuration
OAUTH2_ENABLED=${config.oauth2Enabled}
KEYCLOAK_BASE_URL=https://keycloak.ingka-dt.cn/auth
KEYCLOAK_REALM=master
OAUTH2_CLIENT_ID=mcp-mpc-odi
OAUTH2_CLIENT_SECRET=test-secret
OAUTH2_ADMIN_SCOPES=admin,roles
OAUTH2_WRITE_SCOPES=write
OAUTH2_DEFAULT_SCOPE_VALIDATION=any

# Connection Authentication
CONNECTION_AUTH_ENABLED=${config.connectionAuthEnabled}
CONNECTION_AUTH_STRICT=${config.connectionAuthStrict}
CONNECTION_AUTH_TEST_API=false

# Per-Request Authentication
PER_REQUEST_AUTH_ENABLED=${config.perRequestAuthEnabled}
PER_REQUEST_AUTH_CACHE_ENABLED=true
PER_REQUEST_AUTH_CACHE_MAX_AGE=300
PER_REQUEST_AUTH_LOG_VALIDATION=false

# Debug
DEBUG_SERVICE_ADAPTER=false
`;

    writeFileSync(envPath, baseConfig);
    console.log(colorize(`📝 Updated .env for test: ${config.name}`, 'blue'));
  }

  async startServer() {
    return new Promise((resolve, reject) => {
      console.log(colorize('🚀 Starting MCP server...', 'blue'));
      
      this.serverProcess = spawn('npm', ['run', 'dev'], {
        stdio: ['pipe', 'pipe', 'pipe'],
        cwd: process.cwd()
      });

      let output = '';
      let started = false;

      this.serverProcess.stdout.on('data', (data) => {
        output += data.toString();
        if (output.includes('orders-portal-mcp-server running') && !started) {
          started = true;
          setTimeout(() => resolve(), 2000); // Wait 2s for full startup
        }
      });

      this.serverProcess.stderr.on('data', (data) => {
        output += data.toString();
        if (output.includes('orders-portal-mcp-server running') && !started) {
          started = true;
          setTimeout(() => resolve(), 2000); // Wait 2s for full startup
        }
      });

      this.serverProcess.on('error', (error) => {
        reject(new Error(`Failed to start server: ${error.message}`));
      });

      // Timeout after 30 seconds
      setTimeout(() => {
        if (!started) {
          reject(new Error('Server startup timeout'));
        }
      }, 30000);
    });
  }

  async stopServer() {
    if (this.serverProcess) {
      console.log(colorize('🛑 Stopping MCP server...', 'blue'));
      this.serverProcess.kill('SIGTERM');
      
      // Wait for graceful shutdown
      await new Promise(resolve => {
        this.serverProcess.on('exit', resolve);
        setTimeout(() => {
          this.serverProcess.kill('SIGKILL');
          resolve();
        }, 5000);
      });
      
      this.serverProcess = null;
    }
  }

  async makeRequest(path, options = {}) {
    const url = `http://localhost:${this.testPort}${path}`;
    const method = options.method || 'GET';
    const headers = options.headers || {};
    const body = options.body;

    try {
      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
          ...headers
        },
        body: body ? JSON.stringify(body) : undefined
      });

      const text = await response.text();
      let data;
      try {
        data = JSON.parse(text);
      } catch {
        data = text;
      }

      return {
        status: response.status,
        headers: Object.fromEntries(response.headers.entries()),
        data
      };
    } catch (error) {
      throw new Error(`Request failed: ${error.message}`);
    }
  }

  async testOAuth2Disabled() {
    const config = {
      name: 'OAuth2 Disabled',
      oauth2Enabled: 'false',
      connectionAuthEnabled: 'false',
      connectionAuthStrict: 'false',
      perRequestAuthEnabled: 'false'
    };

    await this.updateEnvForTest(config);
    await this.startServer();

    try {
      // Test 1: Health endpoint should work
      await this.runTest('Health endpoint (no auth)', async () => {
        const response = await this.makeRequest('/health');
        if (response.status !== 200) throw new Error(`Expected 200, got ${response.status}`);
        if (response.data.status !== 'healthy') throw new Error('Server not healthy');
        return 'Health endpoint accessible without authentication';
      });

      // Test 2: Capabilities endpoint should work
      await this.runTest('Capabilities endpoint (no auth)', async () => {
        const response = await this.makeRequest('/mcp/capabilities');
        if (response.status !== 200) throw new Error(`Expected 200, got ${response.status}`);
        if (response.data.oauth2_enabled !== false) throw new Error('OAuth2 should be disabled');
        return 'Capabilities show OAuth2 disabled';
      });

      // Test 3: OAuth2 metadata should return 404
      await this.runTest('OAuth2 metadata disabled', async () => {
        const response = await this.makeRequest('/.well-known/oauth-protected-resource');
        if (response.status !== 404) throw new Error(`Expected 404, got ${response.status}`);
        return 'OAuth2 metadata endpoints properly disabled';
      });

      // Test 4: MCP initialize without auth should work
      await this.runTest('MCP initialize (no auth required)', async () => {
        const response = await this.makeRequest('/mcp', {
          method: 'POST',
          headers: {
            'Accept': 'application/json, text/event-stream'
          },
          body: {
            jsonrpc: '2.0',
            id: 1,
            method: 'initialize',
            params: {
              protocolVersion: '2024-11-05',
              capabilities: {},
              clientInfo: { name: 'test-client', version: '1.0.0' }
            }
          }
        });
        
        if (response.status !== 200) throw new Error(`Expected 200, got ${response.status}`);
        if (!response.data.includes('result')) throw new Error('Invalid MCP response');
        return 'MCP initialize works without authentication';
      });

    } finally {
      await this.stopServer();
    }
  }

  async testOAuth2Enabled() {
    const config = {
      name: 'OAuth2 Enabled',
      oauth2Enabled: 'true',
      connectionAuthEnabled: 'true',
      connectionAuthStrict: 'true',
      perRequestAuthEnabled: 'true'
    };

    await this.updateEnvForTest(config);
    await this.startServer();

    try {
      // Test 1: Health endpoint should still work (no auth required)
      await this.runTest('Health endpoint (public)', async () => {
        const response = await this.makeRequest('/health');
        if (response.status !== 200) throw new Error(`Expected 200, got ${response.status}`);
        return 'Health endpoint remains public';
      });

      // Test 2: Capabilities should show OAuth2 enabled
      await this.runTest('Capabilities show OAuth2 enabled', async () => {
        const response = await this.makeRequest('/mcp/capabilities');
        if (response.status !== 200) throw new Error(`Expected 200, got ${response.status}`);
        if (response.data.oauth2_enabled !== true) throw new Error('OAuth2 should be enabled');
        return 'Capabilities correctly show OAuth2 enabled';
      });

      // Test 3: OAuth2 metadata should be available
      await this.runTest('OAuth2 metadata available', async () => {
        const response = await this.makeRequest('/.well-known/oauth-protected-resource');
        if (response.status !== 200) throw new Error(`Expected 200, got ${response.status}`);
        if (!response.data.resource_metadata) throw new Error('Missing resource metadata');
        return 'OAuth2 metadata endpoints available';
      });

      // Test 4: MCP initialize without auth should fail
      await this.runTest('MCP initialize requires auth', async () => {
        const response = await this.makeRequest('/mcp', {
          method: 'POST',
          headers: {
            'Accept': 'application/json, text/event-stream'
          },
          body: {
            jsonrpc: '2.0',
            id: 1,
            method: 'initialize',
            params: {
              protocolVersion: '2024-11-05',
              capabilities: {},
              clientInfo: { name: 'test-client', version: '1.0.0' }
            }
          }
        });
        
        if (response.status !== 401) throw new Error(`Expected 401, got ${response.status}`);
        if (response.data.error !== 'invalid_token') throw new Error('Expected invalid_token error');
        return 'MCP initialize properly requires authentication';
      });

      // Test 5: MCP initialize with cookie auth should work
      await this.runTest('MCP initialize with cookie auth', async () => {
        const response = await this.makeRequest('/mcp', {
          method: 'POST',
          headers: {
            'Accept': 'application/json, text/event-stream',
            'Cookie': 'test_orders-portal=test-cookie-value'
          },
          body: {
            jsonrpc: '2.0',
            id: 1,
            method: 'initialize',
            params: {
              protocolVersion: '2024-11-05',
              capabilities: {},
              clientInfo: { name: 'test-client', version: '1.0.0' }
            }
          }
        });
        
        // Note: This might fail due to cookie validation, but should not be 401
        if (response.status === 401 && response.data.error === 'invalid_token') {
          throw new Error('Cookie authentication not working');
        }
        return 'Cookie authentication mechanism active';
      });

    } finally {
      await this.stopServer();
    }
  }

  async runTest(name, testFn) {
    try {
      console.log(colorize(`  🧪 ${name}...`, 'yellow'));
      const result = await testFn();
      console.log(colorize(`  ✅ ${name}: ${result}`, 'green'));
      this.results.push({ name, status: 'PASS', message: result });
    } catch (error) {
      console.log(colorize(`  ❌ ${name}: ${error.message}`, 'red'));
      this.results.push({ name, status: 'FAIL', message: error.message });
    }
  }

  printSummary() {
    console.log(colorize('\n📊 Test Results Summary', 'bold'));
    console.log('='.repeat(60));

    const passed = this.results.filter(r => r.status === 'PASS').length;
    const failed = this.results.filter(r => r.status === 'FAIL').length;
    const total = this.results.length;

    console.log(colorize(`Total Tests: ${total}`, 'blue'));
    console.log(colorize(`Passed: ${passed}`, 'green'));
    console.log(colorize(`Failed: ${failed}`, failed > 0 ? 'red' : 'green'));

    if (failed > 0) {
      console.log(colorize('\n❌ Failed Tests:', 'red'));
      this.results
        .filter(r => r.status === 'FAIL')
        .forEach(r => console.log(colorize(`  - ${r.name}: ${r.message}`, 'red')));
    }

    console.log(colorize(`\n🎯 Success Rate: ${Math.round((passed / total) * 100)}%`, 
      passed === total ? 'green' : 'yellow'));
  }

  async cleanup() {
    await this.stopServer();
    await this.restoreEnv();
    console.log(colorize('\n🧹 Cleanup completed', 'blue'));
  }
}

// Run the test suite
const testSuite = new TestSuite();
testSuite.run().catch(console.error);
