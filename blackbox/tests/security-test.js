#!/usr/bin/env node

/**
 * Security Test Suite
 * 
 * Tests authentication, authorization, input validation,
 * and security vulnerabilities
 */

const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  bold: '\x1b[1m'
};

function colorize(text, color) {
  return `${colors[color]}${text}${colors.reset}`;
}

class SecurityTest {
  constructor() {
    this.oauth2DisabledUrl = process.env.MCP_SERVER_OAUTH2_DISABLED_URL || 'http://localhost:3000';
    this.oauth2EnabledUrl = process.env.MCP_SERVER_OAUTH2_ENABLED_URL || 'http://localhost:3001';
    this.results = [];
  }

  async runTests() {
    console.log(colorize('\n🔒 Security Test Suite', 'bold'));
    console.log(colorize(`🔓 OAuth2 Disabled: ${this.oauth2DisabledUrl}`, 'blue'));
    console.log(colorize(`🔐 OAuth2 Enabled: ${this.oauth2EnabledUrl}`, 'blue'));
    console.log('='.repeat(60));

    try {
      // Authentication bypass attempts
      await this.testAuthenticationBypass();
      
      // Authorization checks
      await this.testAuthorizationChecks();
      
      // Input validation
      await this.testInputValidation();
      
      // Rate limiting
      await this.testRateLimiting();
      
      // CORS policy
      await this.testCorsPolicy();
      
      // Information disclosure
      await this.testInformationDisclosure();

      this.printResults();

    } catch (error) {
      console.error(colorize(`❌ Security test failed: ${error.message}`, 'red'));
      process.exit(1);
    }
  }

  async testAuthenticationBypass() {
    console.log(colorize('\n🔓 Authentication Bypass Tests', 'cyan'));
    
    await this.runTest('OAuth2 disabled server access', async () => {
      const response = await fetch(`${this.oauth2DisabledUrl}/mcp`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json, text/event-stream'
        },
        body: JSON.stringify({
          jsonrpc: '2.0',
          id: 1,
          method: 'initialize',
          params: {
            protocolVersion: '2024-11-05',
            capabilities: {},
            clientInfo: { name: 'security-test', version: '1.0.0' }
          }
        })
      });
      
      if (response.status === 200) {
        return 'OAuth2 disabled server allows access without authentication (expected)';
      } else {
        return `OAuth2 disabled server response: ${response.status}`;
      }
    });

    await this.runTest('OAuth2 enabled server without auth', async () => {
      const response = await fetch(`${this.oauth2EnabledUrl}/mcp`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json, text/event-stream'
        },
        body: JSON.stringify({
          jsonrpc: '2.0',
          id: 1,
          method: 'initialize',
          params: {
            protocolVersion: '2024-11-05',
            capabilities: {},
            clientInfo: { name: 'security-test', version: '1.0.0' }
          }
        })
      });
      
      if (response.status === 401) {
        return 'OAuth2 enabled server correctly requires authentication';
      } else if (response.status === 200) {
        return 'OAuth2 enabled server allows access without authentication (security issue!)';
      } else {
        return `OAuth2 enabled server response: ${response.status}`;
      }
    });
  }

  async testAuthorizationChecks() {
    console.log(colorize('\n🛡️ Authorization Checks', 'cyan'));
    
    await this.runTest('Invalid token handling', async () => {
      const response = await fetch(`${this.oauth2EnabledUrl}/mcp`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json, text/event-stream',
          'Authorization': 'Bearer invalid-token-12345'
        },
        body: JSON.stringify({
          jsonrpc: '2.0',
          id: 1,
          method: 'initialize',
          params: {
            protocolVersion: '2024-11-05',
            capabilities: {},
            clientInfo: { name: 'security-test', version: '1.0.0' }
          }
        })
      });
      
      if (response.status === 401) {
        return 'Correctly rejects invalid bearer token';
      } else {
        return `Response to invalid token: ${response.status}`;
      }
    });

    await this.runTest('Malformed Authorization header', async () => {
      const response = await fetch(`${this.oauth2EnabledUrl}/mcp`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json, text/event-stream',
          'Authorization': 'NotBearer malformed-auth'
        },
        body: JSON.stringify({
          jsonrpc: '2.0',
          id: 1,
          method: 'initialize',
          params: {}
        })
      });
      
      if (response.status === 401) {
        return 'Correctly rejects malformed Authorization header';
      } else {
        return `Response to malformed auth: ${response.status}`;
      }
    });
  }

  async testInputValidation() {
    console.log(colorize('\n✅ Input Validation', 'cyan'));
    
    await this.runTest('Path traversal attempt', async () => {
      const response = await fetch(`${this.oauth2DisabledUrl}/../../../etc/passwd`);
      
      if (response.status === 404) {
        return 'Correctly blocks path traversal attempts';
      } else {
        return `Path traversal response: ${response.status}`;
      }
    });

    await this.runTest('Header injection attempt', async () => {
      const response = await fetch(`${this.oauth2DisabledUrl}/health`, {
        headers: {
          'X-Injected-Header': 'test\r\nX-Evil-Header: injected'
        }
      });
      
      if (response.status === 200) {
        return 'Server handles header injection attempt';
      } else {
        return `Header injection response: ${response.status}`;
      }
    });
  }

  async testRateLimiting() {
    console.log(colorize('\n🚦 Rate Limiting', 'cyan'));
    
    await this.runTest('Rapid request detection', async () => {
      const promises = [];
      for (let i = 0; i < 50; i++) {
        promises.push(
          fetch(`${this.oauth2DisabledUrl}/health`).catch(e => ({ error: e.message }))
        );
      }
      
      const results = await Promise.allSettled(promises);
      const rateLimited = results.some(r => 
        r.status === 'fulfilled' && 
        (r.value.status === 429 || r.value.status === 503)
      );
      
      if (rateLimited) {
        return 'Server implements rate limiting';
      } else {
        return 'Server allows rapid requests (no rate limiting detected)';
      }
    });
  }

  async testCorsPolicy() {
    console.log(colorize('\n🌐 CORS Policy', 'cyan'));
    
    await this.runTest('CORS headers', async () => {
      const response = await fetch(`${this.oauth2DisabledUrl}/health`, {
        method: 'OPTIONS',
        headers: {
          'Origin': 'https://evil.example.com',
          'Access-Control-Request-Method': 'POST'
        }
      });
      
      const corsHeader = response.headers.get('Access-Control-Allow-Origin');
      if (corsHeader === '*') {
        return 'Server allows all origins (permissive CORS)';
      } else if (corsHeader) {
        return `Server has specific CORS policy: ${corsHeader}`;
      } else {
        return 'Server has no CORS headers (restrictive)';
      }
    });
  }

  async testInformationDisclosure() {
    console.log(colorize('\n📋 Information Disclosure', 'cyan'));
    
    await this.runTest('Error message information leakage', async () => {
      const response = await fetch(`${this.oauth2DisabledUrl}/nonexistent-endpoint`);
      const text = await response.text();
      
      // Check for sensitive information in error messages
      const sensitivePatterns = [
        /\/[a-zA-Z0-9_\-\/]+\.js/,  // File paths
        /Error: .+ at .+:\d+:\d+/,   // Stack traces
        /password|secret|key|token/i // Sensitive keywords
      ];
      
      const hasSensitiveInfo = sensitivePatterns.some(pattern => pattern.test(text));
      
      if (hasSensitiveInfo) {
        return 'Error messages may contain sensitive information';
      } else {
        return 'Error messages appear sanitized';
      }
    });

    await this.runTest('Server version disclosure', async () => {
      const response = await fetch(`${this.oauth2DisabledUrl}/health`);
      const serverHeader = response.headers.get('Server');
      const xPoweredBy = response.headers.get('X-Powered-By');
      
      if (serverHeader || xPoweredBy) {
        return `Server version disclosed: ${serverHeader || xPoweredBy}`;
      } else {
        return 'Server version information hidden';
      }
    });
  }

  async runTest(name, testFn) {
    try {
      console.log(colorize(`  🧪 ${name}...`, 'yellow'));
      const result = await testFn();
      console.log(colorize(`  ✅ ${name}: ${result}`, 'green'));
      this.results.push({ name, status: 'PASS', message: result });
    } catch (error) {
      console.log(colorize(`  ❌ ${name}: ${error.message}`, 'red'));
      this.results.push({ name, status: 'FAIL', message: error.message });
    }
  }

  printResults() {
    console.log(colorize('\n📊 Security Test Results', 'bold'));
    console.log('='.repeat(60));

    const passed = this.results.filter(r => r.status === 'PASS').length;
    const failed = this.results.filter(r => r.status === 'FAIL').length;
    const total = this.results.length;

    console.log(colorize(`Total Tests: ${total}`, 'blue'));
    console.log(colorize(`Passed: ${passed}`, 'green'));
    console.log(colorize(`Failed: ${failed}`, failed > 0 ? 'red' : 'green'));

    const successRate = Math.round((passed / total) * 100);
    console.log(colorize(`\n🎯 Security Score: ${successRate}%`, 
      successRate >= 90 ? 'green' : successRate >= 70 ? 'yellow' : 'red'));

    // Security-specific warnings
    const securityIssues = this.results.filter(r => 
      r.message.includes('security issue') || 
      r.message.includes('allows access without authentication')
    );

    if (securityIssues.length > 0) {
      console.log(colorize('\n⚠️ Security Issues Found:', 'red'));
      securityIssues.forEach(issue => {
        console.log(colorize(`  - ${issue.name}: ${issue.message}`, 'red'));
      });
    }

    // Export results for CI/CD
    if (process.env.NODE_ENV === 'test') {
      const results = {
        timestamp: new Date().toISOString(),
        servers: {
          oauth2Disabled: this.oauth2DisabledUrl,
          oauth2Enabled: this.oauth2EnabledUrl
        },
        category: 'security',
        summary: { total, passed, failed, successRate },
        securityIssues: securityIssues.length,
        details: this.results
      };
      
      console.log('\n📄 JSON Results:');
      console.log(JSON.stringify(results, null, 2));
    }
  }
}

// Run security tests
const test = new SecurityTest();
test.runTests().catch(console.error);
