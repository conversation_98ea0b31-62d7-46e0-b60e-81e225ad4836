#!/usr/bin/env node

/**
 * MCP Protocol Compliance Test
 * 
 * Tests strict adherence to MCP protocol specification
 * Validates JSON-RPC 2.0 compliance, message formats, and error handling
 */

import { performance } from 'perf_hooks';

const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  bold: '\x1b[1m'
};

function colorize(text, color) {
  return `${colors[color]}${text}${colors.reset}`;
}

class ProtocolComplianceTest {
  constructor(serverUrl = 'http://localhost:3000') {
    this.serverUrl = serverUrl;
    this.results = [];
    this.requestId = 1;
  }

  async runTests() {
    console.log(colorize('\n📋 MCP Protocol Compliance Test', 'bold'));
    console.log(colorize(`🌐 Server: ${this.serverUrl}`, 'blue'));
    console.log('='.repeat(60));

    try {
      // JSON-RPC 2.0 Compliance
      await this.testJsonRpcCompliance();
      
      // MCP Protocol Version
      await this.testProtocolVersion();
      
      // Required Fields Validation
      await this.testRequiredFields();
      
      // Error Response Format
      await this.testErrorResponseFormat();
      
      // Content-Type Validation
      await this.testContentTypeHandling();
      
      // Message Size Limits
      await this.testMessageSizeLimits();
      
      // Invalid JSON Handling
      await this.testInvalidJsonHandling();
      
      // Method Not Found
      await this.testMethodNotFound();
      
      // Parameter Validation
      await this.testParameterValidation();

      this.printResults();

    } catch (error) {
      console.error(colorize(`❌ Protocol compliance test failed: ${error.message}`, 'red'));
      process.exit(1);
    }
  }

  async testJsonRpcCompliance() {
    console.log(colorize('\n🔍 JSON-RPC 2.0 Compliance', 'cyan'));
    
    // Test missing jsonrpc field
    await this.runTest('Missing jsonrpc field', async () => {
      const response = await this.makeRequest('/mcp', {
        method: 'POST',
        body: {
          id: this.requestId++,
          method: 'initialize',
          params: {}
        }
      });
      
      if (response.status !== 400) {
        throw new Error(`Expected 400 for missing jsonrpc, got ${response.status}`);
      }
      return 'Correctly rejects missing jsonrpc field';
    });

    // Test invalid jsonrpc version
    await this.runTest('Invalid jsonrpc version', async () => {
      const response = await this.makeRequest('/mcp', {
        method: 'POST',
        body: {
          jsonrpc: '1.0',
          id: this.requestId++,
          method: 'initialize',
          params: {}
        }
      });
      
      if (response.status !== 400) {
        throw new Error(`Expected 400 for invalid jsonrpc version, got ${response.status}`);
      }
      return 'Correctly rejects invalid jsonrpc version';
    });

    // Test missing method field
    await this.runTest('Missing method field', async () => {
      const response = await this.makeRequest('/mcp', {
        method: 'POST',
        body: {
          jsonrpc: '2.0',
          id: this.requestId++,
          params: {}
        }
      });
      
      if (response.status !== 400) {
        throw new Error(`Expected 400 for missing method, got ${response.status}`);
      }
      return 'Correctly rejects missing method field';
    });
  }

  async testProtocolVersion() {
    console.log(colorize('\n📋 MCP Protocol Version', 'cyan'));
    
    await this.runTest('Supported protocol version', async () => {
      const response = await this.makeRequest('/mcp', {
        method: 'POST',
        body: {
          jsonrpc: '2.0',
          id: this.requestId++,
          method: 'initialize',
          params: {
            protocolVersion: '2024-11-05',
            capabilities: {},
            clientInfo: { name: 'protocol-test', version: '1.0.0' }
          }
        }
      });
      
      if (response.status !== 200) {
        throw new Error(`Initialize failed: ${response.status}`);
      }
      
      const data = this.parseSSEResponse(response.data);
      if (!data.result || !data.result.protocolVersion) {
        throw new Error('Missing protocol version in response');
      }
      
      return `Protocol version: ${data.result.protocolVersion}`;
    });

    await this.runTest('Unsupported protocol version', async () => {
      const response = await this.makeRequest('/mcp', {
        method: 'POST',
        body: {
          jsonrpc: '2.0',
          id: this.requestId++,
          method: 'initialize',
          params: {
            protocolVersion: '1999-01-01',
            capabilities: {},
            clientInfo: { name: 'protocol-test', version: '1.0.0' }
          }
        }
      });
      
      const data = this.parseSSEResponse(response.data);
      if (data.error && data.error.code) {
        return `Correctly rejects unsupported protocol version (error: ${data.error.code})`;
      }
      
      // Some servers might still accept it, which is also valid
      return 'Server accepts unsupported protocol version (permissive)';
    });
  }

  async testRequiredFields() {
    console.log(colorize('\n📝 Required Fields Validation', 'cyan'));
    
    await this.runTest('Initialize without clientInfo', async () => {
      const response = await this.makeRequest('/mcp', {
        method: 'POST',
        body: {
          jsonrpc: '2.0',
          id: this.requestId++,
          method: 'initialize',
          params: {
            protocolVersion: '2024-11-05',
            capabilities: {}
          }
        }
      });
      
      const data = this.parseSSEResponse(response.data);
      if (data.error) {
        return `Correctly requires clientInfo (error: ${data.error.code})`;
      }
      
      return 'Server accepts missing clientInfo (permissive)';
    });

    await this.runTest('Initialize without capabilities', async () => {
      const response = await this.makeRequest('/mcp', {
        method: 'POST',
        body: {
          jsonrpc: '2.0',
          id: this.requestId++,
          method: 'initialize',
          params: {
            protocolVersion: '2024-11-05',
            clientInfo: { name: 'protocol-test', version: '1.0.0' }
          }
        }
      });
      
      const data = this.parseSSEResponse(response.data);
      if (data.error) {
        return `Correctly requires capabilities (error: ${data.error.code})`;
      }
      
      return 'Server accepts missing capabilities (permissive)';
    });
  }

  async testErrorResponseFormat() {
    console.log(colorize('\n🚨 Error Response Format', 'cyan'));
    
    await this.runTest('Error response structure', async () => {
      const response = await this.makeRequest('/mcp', {
        method: 'POST',
        body: {
          jsonrpc: '2.0',
          id: this.requestId++,
          method: 'nonexistent_method',
          params: {}
        }
      });
      
      const data = this.parseSSEResponse(response.data);
      if (!data.error) {
        throw new Error('Expected error response for nonexistent method');
      }
      
      if (typeof data.error.code !== 'number') {
        throw new Error('Error code must be a number');
      }
      
      if (typeof data.error.message !== 'string') {
        throw new Error('Error message must be a string');
      }
      
      return `Valid error format (code: ${data.error.code}, message: "${data.error.message}")`;
    });
  }

  async testContentTypeHandling() {
    console.log(colorize('\n📄 Content-Type Handling', 'cyan'));
    
    await this.runTest('Missing Content-Type', async () => {
      const response = await this.makeRequest('/mcp', {
        method: 'POST',
        headers: {
          'Accept': 'application/json, text/event-stream'
        },
        body: {
          jsonrpc: '2.0',
          id: this.requestId++,
          method: 'initialize',
          params: {
            protocolVersion: '2024-11-05',
            capabilities: {},
            clientInfo: { name: 'protocol-test', version: '1.0.0' }
          }
        }
      });
      
      // Should either work or return 400
      if (response.status === 200) {
        return 'Server accepts missing Content-Type (permissive)';
      } else if (response.status === 400) {
        return 'Server requires Content-Type (strict)';
      } else {
        throw new Error(`Unexpected status: ${response.status}`);
      }
    });

    await this.runTest('Invalid Content-Type', async () => {
      const response = await this.makeRequest('/mcp', {
        method: 'POST',
        headers: {
          'Content-Type': 'text/plain',
          'Accept': 'application/json, text/event-stream'
        },
        body: {
          jsonrpc: '2.0',
          id: this.requestId++,
          method: 'initialize',
          params: {
            protocolVersion: '2024-11-05',
            capabilities: {},
            clientInfo: { name: 'protocol-test', version: '1.0.0' }
          }
        }
      });
      
      if (response.status === 415) {
        return 'Correctly rejects invalid Content-Type';
      } else if (response.status === 200) {
        return 'Server accepts invalid Content-Type (permissive)';
      } else {
        return `Unexpected response to invalid Content-Type: ${response.status}`;
      }
    });
  }

  async testMessageSizeLimits() {
    console.log(colorize('\n📏 Message Size Limits', 'cyan'));
    
    await this.runTest('Large message handling', async () => {
      const largeParams = {
        protocolVersion: '2024-11-05',
        capabilities: {},
        clientInfo: { 
          name: 'protocol-test', 
          version: '1.0.0',
          largeData: 'x'.repeat(10000) // 10KB of data
        }
      };
      
      const response = await this.makeRequest('/mcp', {
        method: 'POST',
        body: {
          jsonrpc: '2.0',
          id: this.requestId++,
          method: 'initialize',
          params: largeParams
        }
      });
      
      if (response.status === 200) {
        return 'Server handles large messages (10KB)';
      } else if (response.status === 413) {
        return 'Server rejects large messages (has size limit)';
      } else {
        return `Unexpected response to large message: ${response.status}`;
      }
    });
  }

  async testInvalidJsonHandling() {
    console.log(colorize('\n🔧 Invalid JSON Handling', 'cyan'));
    
    await this.runTest('Malformed JSON', async () => {
      const response = await fetch(`${this.serverUrl}/mcp`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json, text/event-stream'
        },
        body: '{"jsonrpc": "2.0", "id": 1, "method": "initialize", "params": {'
      });
      
      if (response.status === 400) {
        return 'Correctly rejects malformed JSON';
      } else {
        return `Unexpected response to malformed JSON: ${response.status}`;
      }
    });
  }

  async testMethodNotFound() {
    console.log(colorize('\n🔍 Method Not Found', 'cyan'));
    
    await this.runTest('Nonexistent method', async () => {
      const response = await this.makeRequest('/mcp', {
        method: 'POST',
        body: {
          jsonrpc: '2.0',
          id: this.requestId++,
          method: 'definitely_not_a_real_method',
          params: {}
        }
      });
      
      const data = this.parseSSEResponse(response.data);
      if (data.error && data.error.code === -32601) {
        return 'Correctly returns -32601 for method not found';
      } else if (data.error) {
        return `Returns error for nonexistent method (code: ${data.error.code})`;
      } else {
        throw new Error('Should return error for nonexistent method');
      }
    });
  }

  async testParameterValidation() {
    console.log(colorize('\n✅ Parameter Validation', 'cyan'));
    
    await this.runTest('Invalid parameter types', async () => {
      const response = await this.makeRequest('/mcp', {
        method: 'POST',
        body: {
          jsonrpc: '2.0',
          id: this.requestId++,
          method: 'initialize',
          params: "this should be an object"
        }
      });
      
      const data = this.parseSSEResponse(response.data);
      if (data.error) {
        return `Correctly validates parameter types (error: ${data.error.code})`;
      } else {
        return 'Server accepts invalid parameter types (permissive)';
      }
    });
  }

  parseSSEResponse(data) {
    const lines = data.split('\n');
    for (const line of lines) {
      if (line.startsWith('data: ')) {
        try {
          return JSON.parse(line.substring(6));
        } catch (e) {
          continue;
        }
      }
    }
    throw new Error('No valid JSON data found in SSE response');
  }

  async makeRequest(path, options = {}) {
    const url = `${this.serverUrl}${path}`;
    const method = options.method || 'GET';
    const headers = {
      'Content-Type': 'application/json',
      'Accept': 'application/json, text/event-stream',
      ...options.headers
    };
    const body = options.body;

    try {
      const response = await fetch(url, {
        method,
        headers,
        body: body ? JSON.stringify(body) : undefined
      });

      const text = await response.text();

      return {
        status: response.status,
        headers: Object.fromEntries(response.headers.entries()),
        data: text
      };
    } catch (error) {
      throw new Error(`Request failed: ${error.message}`);
    }
  }

  async runTest(name, testFn) {
    try {
      console.log(colorize(`  🧪 ${name}...`, 'yellow'));
      const result = await testFn();
      console.log(colorize(`  ✅ ${name}: ${result}`, 'green'));
      this.results.push({ name, status: 'PASS', message: result });
    } catch (error) {
      console.log(colorize(`  ❌ ${name}: ${error.message}`, 'red'));
      this.results.push({ name, status: 'FAIL', message: error.message });
    }
  }

  printResults() {
    console.log(colorize('\n📊 Protocol Compliance Results', 'bold'));
    console.log('='.repeat(60));

    const passed = this.results.filter(r => r.status === 'PASS').length;
    const failed = this.results.filter(r => r.status === 'FAIL').length;
    const total = this.results.length;

    console.log(colorize(`Total Tests: ${total}`, 'blue'));
    console.log(colorize(`Passed: ${passed}`, 'green'));
    console.log(colorize(`Failed: ${failed}`, failed > 0 ? 'red' : 'green'));

    const successRate = Math.round((passed / total) * 100);
    console.log(colorize(`\n🎯 Protocol Compliance: ${successRate}%`, 
      successRate >= 90 ? 'green' : successRate >= 70 ? 'yellow' : 'red'));

    if (failed > 0) {
      console.log(colorize('\n❌ Failed Tests:', 'red'));
      this.results
        .filter(r => r.status === 'FAIL')
        .forEach(r => console.log(colorize(`  - ${r.name}: ${r.message}`, 'red')));
    }

    // Export results for CI/CD
    if (process.env.NODE_ENV === 'test') {
      const results = {
        timestamp: new Date().toISOString(),
        server: this.serverUrl,
        category: 'protocol-compliance',
        summary: { total, passed, failed, successRate },
        details: this.results
      };
      
      console.log('\n📄 JSON Results:');
      console.log(JSON.stringify(results, null, 2));
    }
  }
}

// Run protocol compliance tests
const serverUrl = process.env.MCP_SERVER_URL || 'http://localhost:3000';
const test = new ProtocolComplianceTest(serverUrl);
test.runTests().catch(console.error);
