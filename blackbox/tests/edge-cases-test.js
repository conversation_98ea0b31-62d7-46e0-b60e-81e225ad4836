#!/usr/bin/env node

/**
 * Edge Cases & Error Handling Test
 * 
 * Tests server behavior under unusual conditions, malformed requests,
 * and error scenarios to ensure robust error handling
 */

import { performance } from 'perf_hooks';

const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  bold: '\x1b[1m'
};

function colorize(text, color) {
  return `${colors[color]}${text}${colors.reset}`;
}

class EdgeCasesTest {
  constructor(serverUrl = 'http://localhost:3000') {
    this.serverUrl = serverUrl;
    this.results = [];
    this.requestId = 1;
  }

  async runTests() {
    console.log(colorize('\n🔍 Edge Cases & Error Handling Test', 'bold'));
    console.log(colorize(`🌐 Server: ${this.serverUrl}`, 'blue'));
    console.log('='.repeat(60));

    try {
      // Connection edge cases
      await this.testConnectionEdgeCases();
      
      // Request format edge cases
      await this.testRequestFormatEdgeCases();
      
      // Parameter edge cases
      await this.testParameterEdgeCases();
      
      // Timeout and slow responses
      await this.testTimeoutHandling();
      
      // Resource exhaustion
      await this.testResourceExhaustion();
      
      // Unicode and encoding
      await this.testUnicodeHandling();
      
      // HTTP method variations
      await this.testHttpMethodVariations();
      
      // Malicious input
      await this.testMaliciousInput();

      this.printResults();

    } catch (error) {
      console.error(colorize(`❌ Edge cases test failed: ${error.message}`, 'red'));
      process.exit(1);
    }
  }

  async testConnectionEdgeCases() {
    console.log(colorize('\n🔌 Connection Edge Cases', 'cyan'));
    
    await this.runTest('Empty request body', async () => {
      const response = await fetch(`${this.serverUrl}/mcp`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json, text/event-stream'
        },
        body: ''
      });
      
      if (response.status === 400) {
        return 'Correctly rejects empty request body';
      } else {
        return `Unexpected response to empty body: ${response.status}`;
      }
    });

    await this.runTest('Null request body', async () => {
      const response = await fetch(`${this.serverUrl}/mcp`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json, text/event-stream'
        },
        body: null
      });
      
      if (response.status === 400) {
        return 'Correctly rejects null request body';
      } else {
        return `Unexpected response to null body: ${response.status}`;
      }
    });

    await this.runTest('Very long URL path', async () => {
      const longPath = '/mcp/' + 'a'.repeat(2000);
      const response = await fetch(`${this.serverUrl}${longPath}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          jsonrpc: '2.0',
          id: this.requestId++,
          method: 'initialize',
          params: {}
        })
      });
      
      if (response.status === 404) {
        return 'Correctly returns 404 for invalid long path';
      } else {
        return `Response to long path: ${response.status}`;
      }
    });
  }

  async testRequestFormatEdgeCases() {
    console.log(colorize('\n📝 Request Format Edge Cases', 'cyan'));
    
    await this.runTest('Array instead of object', async () => {
      const response = await fetch(`${this.serverUrl}/mcp`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json, text/event-stream'
        },
        body: JSON.stringify([{
          jsonrpc: '2.0',
          id: this.requestId++,
          method: 'initialize',
          params: {}
        }])
      });
      
      if (response.status === 400) {
        return 'Correctly rejects array format';
      } else {
        return `Response to array format: ${response.status}`;
      }
    });

    await this.runTest('Deeply nested object', async () => {
      let deepObject = {};
      let current = deepObject;
      for (let i = 0; i < 100; i++) {
        current.nested = {};
        current = current.nested;
      }
      current.value = 'deep';

      const response = await this.makeRequest('/mcp', {
        method: 'POST',
        body: {
          jsonrpc: '2.0',
          id: this.requestId++,
          method: 'initialize',
          params: {
            protocolVersion: '2024-11-05',
            capabilities: {},
            clientInfo: { name: 'edge-test', version: '1.0.0' },
            deepData: deepObject
          }
        }
      });
      
      if (response.status === 200) {
        return 'Handles deeply nested objects';
      } else if (response.status === 400) {
        return 'Rejects deeply nested objects (has depth limit)';
      } else {
        return `Response to deep nesting: ${response.status}`;
      }
    });

    await this.runTest('Circular reference handling', async () => {
      // This should be caught by JSON.stringify, but test server handling
      try {
        const obj = { name: 'test' };
        obj.self = obj; // Create circular reference
        
        const response = await this.makeRequest('/mcp', {
          method: 'POST',
          body: {
            jsonrpc: '2.0',
            id: this.requestId++,
            method: 'initialize',
            params: {
              protocolVersion: '2024-11-05',
              capabilities: {},
              clientInfo: obj
            }
          }
        });
        
        return 'Unexpected: circular reference was serialized';
      } catch (error) {
        if (error.message.includes('circular')) {
          return 'Client correctly prevents circular references';
        } else {
          throw error;
        }
      }
    });
  }

  async testParameterEdgeCases() {
    console.log(colorize('\n🎯 Parameter Edge Cases', 'cyan'));
    
    await this.runTest('Null parameters', async () => {
      const response = await this.makeRequest('/mcp', {
        method: 'POST',
        body: {
          jsonrpc: '2.0',
          id: this.requestId++,
          method: 'initialize',
          params: null
        }
      });
      
      const data = this.parseSSEResponse(response.data);
      if (data.error) {
        return `Correctly handles null params (error: ${data.error.code})`;
      } else {
        return 'Accepts null parameters';
      }
    });

    await this.runTest('Missing required parameters', async () => {
      const response = await this.makeRequest('/mcp', {
        method: 'POST',
        body: {
          jsonrpc: '2.0',
          id: this.requestId++,
          method: 'initialize',
          params: {}
        }
      });
      
      const data = this.parseSSEResponse(response.data);
      if (data.error) {
        return `Validates required parameters (error: ${data.error.code})`;
      } else {
        return 'Accepts missing required parameters (permissive)';
      }
    });

    await this.runTest('Extra unknown parameters', async () => {
      const response = await this.makeRequest('/mcp', {
        method: 'POST',
        body: {
          jsonrpc: '2.0',
          id: this.requestId++,
          method: 'initialize',
          params: {
            protocolVersion: '2024-11-05',
            capabilities: {},
            clientInfo: { name: 'edge-test', version: '1.0.0' },
            unknownParam: 'should be ignored',
            anotherUnknown: { complex: 'object' }
          }
        }
      });
      
      const data = this.parseSSEResponse(response.data);
      if (data.result) {
        return 'Ignores unknown parameters (permissive)';
      } else if (data.error) {
        return `Rejects unknown parameters (strict): ${data.error.code}`;
      } else {
        throw new Error('Unexpected response format');
      }
    });
  }

  async testTimeoutHandling() {
    console.log(colorize('\n⏱️ Timeout Handling', 'cyan'));
    
    await this.runTest('Request timeout behavior', async () => {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 100); // 100ms timeout
      
      try {
        const response = await fetch(`${this.serverUrl}/mcp`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json, text/event-stream'
          },
          body: JSON.stringify({
            jsonrpc: '2.0',
            id: this.requestId++,
            method: 'initialize',
            params: {
              protocolVersion: '2024-11-05',
              capabilities: {},
              clientInfo: { name: 'edge-test', version: '1.0.0' }
            }
          }),
          signal: controller.signal
        });
        
        clearTimeout(timeoutId);
        return `Server responds within 100ms (status: ${response.status})`;
      } catch (error) {
        clearTimeout(timeoutId);
        if (error.name === 'AbortError') {
          return 'Server takes longer than 100ms to respond';
        } else {
          throw error;
        }
      }
    });
  }

  async testResourceExhaustion() {
    console.log(colorize('\n💾 Resource Exhaustion', 'cyan'));
    
    await this.runTest('Large parameter values', async () => {
      const largeString = 'x'.repeat(100000); // 100KB string
      
      const response = await this.makeRequest('/mcp', {
        method: 'POST',
        body: {
          jsonrpc: '2.0',
          id: this.requestId++,
          method: 'initialize',
          params: {
            protocolVersion: '2024-11-05',
            capabilities: {},
            clientInfo: { 
              name: 'edge-test', 
              version: '1.0.0',
              largeData: largeString
            }
          }
        }
      });
      
      if (response.status === 200) {
        return 'Handles large parameter values (100KB)';
      } else if (response.status === 413) {
        return 'Rejects large parameter values (has size limit)';
      } else {
        return `Response to large params: ${response.status}`;
      }
    });

    await this.runTest('Many parameters', async () => {
      const manyParams = {};
      for (let i = 0; i < 1000; i++) {
        manyParams[`param${i}`] = `value${i}`;
      }
      
      const response = await this.makeRequest('/mcp', {
        method: 'POST',
        body: {
          jsonrpc: '2.0',
          id: this.requestId++,
          method: 'initialize',
          params: {
            protocolVersion: '2024-11-05',
            capabilities: {},
            clientInfo: { name: 'edge-test', version: '1.0.0' },
            ...manyParams
          }
        }
      });
      
      if (response.status === 200) {
        return 'Handles many parameters (1000)';
      } else {
        return `Response to many params: ${response.status}`;
      }
    });
  }

  async testUnicodeHandling() {
    console.log(colorize('\n🌍 Unicode Handling', 'cyan'));
    
    await this.runTest('Unicode characters', async () => {
      const unicodeText = '🚀 Hello 世界 🌟 Ñoël 🎉 العربية 🔥';
      
      const response = await this.makeRequest('/mcp', {
        method: 'POST',
        body: {
          jsonrpc: '2.0',
          id: this.requestId++,
          method: 'initialize',
          params: {
            protocolVersion: '2024-11-05',
            capabilities: {},
            clientInfo: { 
              name: unicodeText, 
              version: '1.0.0'
            }
          }
        }
      });
      
      if (response.status === 200) {
        return 'Handles Unicode characters correctly';
      } else {
        return `Response to Unicode: ${response.status}`;
      }
    });

    await this.runTest('Control characters', async () => {
      const controlChars = '\x00\x01\x02\x03\x04\x05\x06\x07\x08\x09\x0A\x0B\x0C\x0D\x0E\x0F';
      
      const response = await this.makeRequest('/mcp', {
        method: 'POST',
        body: {
          jsonrpc: '2.0',
          id: this.requestId++,
          method: 'initialize',
          params: {
            protocolVersion: '2024-11-05',
            capabilities: {},
            clientInfo: { 
              name: 'edge-test', 
              version: '1.0.0',
              controlData: controlChars
            }
          }
        }
      });
      
      if (response.status === 200) {
        return 'Handles control characters';
      } else if (response.status === 400) {
        return 'Rejects control characters (sanitized)';
      } else {
        return `Response to control chars: ${response.status}`;
      }
    });
  }

  async testHttpMethodVariations() {
    console.log(colorize('\n🌐 HTTP Method Variations', 'cyan'));
    
    await this.runTest('GET request to MCP endpoint', async () => {
      const response = await fetch(`${this.serverUrl}/mcp`, {
        method: 'GET'
      });
      
      if (response.status === 405) {
        return 'Correctly rejects GET method (405 Method Not Allowed)';
      } else if (response.status === 200) {
        return 'Accepts GET method (returns capabilities or info)';
      } else {
        return `Response to GET: ${response.status}`;
      }
    });

    await this.runTest('PUT request to MCP endpoint', async () => {
      const response = await fetch(`${this.serverUrl}/mcp`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          jsonrpc: '2.0',
          id: this.requestId++,
          method: 'initialize',
          params: {}
        })
      });
      
      if (response.status === 405) {
        return 'Correctly rejects PUT method';
      } else {
        return `Response to PUT: ${response.status}`;
      }
    });
  }

  async testMaliciousInput() {
    console.log(colorize('\n🛡️ Malicious Input', 'cyan'));
    
    await this.runTest('SQL injection attempt', async () => {
      const sqlInjection = "'; DROP TABLE users; --";
      
      const response = await this.makeRequest('/mcp', {
        method: 'POST',
        body: {
          jsonrpc: '2.0',
          id: this.requestId++,
          method: 'initialize',
          params: {
            protocolVersion: '2024-11-05',
            capabilities: {},
            clientInfo: { 
              name: sqlInjection, 
              version: '1.0.0'
            }
          }
        }
      });
      
      if (response.status === 200) {
        return 'Handles SQL injection attempt (treated as normal string)';
      } else {
        return `Response to SQL injection: ${response.status}`;
      }
    });

    await this.runTest('XSS attempt', async () => {
      const xssPayload = '<script>alert("xss")</script>';
      
      const response = await this.makeRequest('/mcp', {
        method: 'POST',
        body: {
          jsonrpc: '2.0',
          id: this.requestId++,
          method: 'initialize',
          params: {
            protocolVersion: '2024-11-05',
            capabilities: {},
            clientInfo: { 
              name: xssPayload, 
              version: '1.0.0'
            }
          }
        }
      });
      
      if (response.status === 200) {
        return 'Handles XSS attempt (treated as normal string)';
      } else {
        return `Response to XSS: ${response.status}`;
      }
    });
  }

  parseSSEResponse(data) {
    const lines = data.split('\n');
    for (const line of lines) {
      if (line.startsWith('data: ')) {
        try {
          return JSON.parse(line.substring(6));
        } catch (e) {
          continue;
        }
      }
    }
    throw new Error('No valid JSON data found in SSE response');
  }

  async makeRequest(path, options = {}) {
    const url = `${this.serverUrl}${path}`;
    const method = options.method || 'GET';
    const headers = {
      'Content-Type': 'application/json',
      'Accept': 'application/json, text/event-stream',
      ...options.headers
    };
    const body = options.body;

    try {
      const response = await fetch(url, {
        method,
        headers,
        body: body ? JSON.stringify(body) : undefined
      });

      const text = await response.text();

      return {
        status: response.status,
        headers: Object.fromEntries(response.headers.entries()),
        data: text
      };
    } catch (error) {
      throw new Error(`Request failed: ${error.message}`);
    }
  }

  async runTest(name, testFn) {
    try {
      console.log(colorize(`  🧪 ${name}...`, 'yellow'));
      const result = await testFn();
      console.log(colorize(`  ✅ ${name}: ${result}`, 'green'));
      this.results.push({ name, status: 'PASS', message: result });
    } catch (error) {
      console.log(colorize(`  ❌ ${name}: ${error.message}`, 'red'));
      this.results.push({ name, status: 'FAIL', message: error.message });
    }
  }

  printResults() {
    console.log(colorize('\n📊 Edge Cases Test Results', 'bold'));
    console.log('='.repeat(60));

    const passed = this.results.filter(r => r.status === 'PASS').length;
    const failed = this.results.filter(r => r.status === 'FAIL').length;
    const total = this.results.length;

    console.log(colorize(`Total Tests: ${total}`, 'blue'));
    console.log(colorize(`Passed: ${passed}`, 'green'));
    console.log(colorize(`Failed: ${failed}`, failed > 0 ? 'red' : 'green'));

    const successRate = Math.round((passed / total) * 100);
    console.log(colorize(`\n🎯 Edge Case Handling: ${successRate}%`, 
      successRate >= 90 ? 'green' : successRate >= 70 ? 'yellow' : 'red'));

    // Export results for CI/CD
    if (process.env.NODE_ENV === 'test') {
      const results = {
        timestamp: new Date().toISOString(),
        server: this.serverUrl,
        category: 'edge-cases',
        summary: { total, passed, failed, successRate },
        details: this.results
      };
      
      console.log('\n📄 JSON Results:');
      console.log(JSON.stringify(results, null, 2));
    }
  }
}

// Run edge cases tests
const serverUrl = process.env.MCP_SERVER_URL || 'http://localhost:3000';
const test = new EdgeCasesTest(serverUrl);
test.runTests().catch(console.error);
