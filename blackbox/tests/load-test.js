#!/usr/bin/env node

/**
 * Load & Concurrency Test
 * 
 * Tests server performance under load, concurrent connections,
 * and sustained traffic patterns
 */

import { performance } from 'perf_hooks';

const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  bold: '\x1b[1m'
};

function colorize(text, color) {
  return `${colors[color]}${text}${colors.reset}`;
}

class LoadTest {
  constructor(serverUrl = 'http://localhost:3000') {
    this.serverUrl = serverUrl;
    this.results = [];
    this.requestId = 1;
    this.concurrentClients = parseInt(process.env.CONCURRENT_CLIENTS) || 10;
    this.testDuration = parseInt(process.env.TEST_DURATION) || 30; // seconds
  }

  async runTests() {
    console.log(colorize('\n🔥 Load & Concurrency Test', 'bold'));
    console.log(colorize(`🌐 Server: ${this.serverUrl}`, 'blue'));
    console.log(colorize(`👥 Concurrent Clients: ${this.concurrentClients}`, 'blue'));
    console.log(colorize(`⏱️ Test Duration: ${this.testDuration}s`, 'blue'));
    console.log('='.repeat(60));

    try {
      // Basic load test
      await this.testBasicLoad();
      
      // Concurrent connections
      await this.testConcurrentConnections();
      
      // Sustained load
      await this.testSustainedLoad();
      
      // Burst traffic
      await this.testBurstTraffic();
      
      // Memory usage patterns
      await this.testMemoryUsage();
      
      // Connection limits
      await this.testConnectionLimits();

      this.printResults();

    } catch (error) {
      console.error(colorize(`❌ Load test failed: ${error.message}`, 'red'));
      process.exit(1);
    }
  }

  async testBasicLoad() {
    console.log(colorize('\n⚡ Basic Load Test', 'cyan'));
    
    await this.runTest('Sequential requests', async () => {
      const iterations = 100;
      const startTime = performance.now();
      let successful = 0;
      let failed = 0;
      const responseTimes = [];

      for (let i = 0; i < iterations; i++) {
        const requestStart = performance.now();
        try {
          const response = await this.makeHealthRequest();
          const requestTime = performance.now() - requestStart;
          responseTimes.push(requestTime);
          
          if (response.status === 200) {
            successful++;
          } else {
            failed++;
          }
        } catch (error) {
          failed++;
        }
      }

      const totalTime = performance.now() - startTime;
      const avgResponseTime = responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length;
      const requestsPerSecond = (iterations / totalTime) * 1000;

      return `${successful}/${iterations} successful, ${avgResponseTime.toFixed(0)}ms avg, ${requestsPerSecond.toFixed(1)} req/s`;
    });
  }

  async testConcurrentConnections() {
    console.log(colorize('\n👥 Concurrent Connections', 'cyan'));
    
    await this.runTest(`${this.concurrentClients} concurrent requests`, async () => {
      const startTime = performance.now();
      const promises = [];

      for (let i = 0; i < this.concurrentClients; i++) {
        promises.push(this.makeHealthRequest());
      }

      const results = await Promise.allSettled(promises);
      const totalTime = performance.now() - startTime;
      
      const successful = results.filter(r => r.status === 'fulfilled' && r.value.status === 200).length;
      const failed = results.length - successful;
      const requestsPerSecond = (this.concurrentClients / totalTime) * 1000;

      return `${successful}/${this.concurrentClients} successful, ${totalTime.toFixed(0)}ms total, ${requestsPerSecond.toFixed(1)} req/s`;
    });

    await this.runTest('Concurrent MCP initialize', async () => {
      const startTime = performance.now();
      const promises = [];

      for (let i = 0; i < Math.min(this.concurrentClients, 20); i++) {
        promises.push(this.makeMCPInitializeRequest());
      }

      const results = await Promise.allSettled(promises);
      const totalTime = performance.now() - startTime;
      
      const successful = results.filter(r => {
        if (r.status === 'fulfilled') {
          try {
            const data = this.parseSSEResponse(r.value.data);
            return data.result && data.result.protocolVersion;
          } catch {
            return false;
          }
        }
        return false;
      }).length;
      
      const failed = results.length - successful;

      return `${successful}/${results.length} MCP initializations successful in ${totalTime.toFixed(0)}ms`;
    });
  }

  async testSustainedLoad() {
    console.log(colorize('\n🔄 Sustained Load Test', 'cyan'));
    
    await this.runTest(`Sustained load for ${this.testDuration}s`, async () => {
      const endTime = Date.now() + (this.testDuration * 1000);
      let totalRequests = 0;
      let successful = 0;
      let failed = 0;
      const responseTimes = [];

      while (Date.now() < endTime) {
        const batchPromises = [];
        const batchSize = Math.min(this.concurrentClients, 10);
        
        for (let i = 0; i < batchSize; i++) {
          const requestStart = performance.now();
          batchPromises.push(
            this.makeHealthRequest()
              .then(response => {
                const requestTime = performance.now() - requestStart;
                responseTimes.push(requestTime);
                return response;
              })
              .catch(error => {
                const requestTime = performance.now() - requestStart;
                responseTimes.push(requestTime);
                throw error;
              })
          );
        }

        const results = await Promise.allSettled(batchPromises);
        totalRequests += results.length;
        
        results.forEach(result => {
          if (result.status === 'fulfilled' && result.value.status === 200) {
            successful++;
          } else {
            failed++;
          }
        });

        // Small delay to prevent overwhelming
        await new Promise(resolve => setTimeout(resolve, 100));
      }

      const avgResponseTime = responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length;
      const requestsPerSecond = totalRequests / this.testDuration;
      const successRate = (successful / totalRequests) * 100;

      return `${totalRequests} total requests, ${successRate.toFixed(1)}% success, ${avgResponseTime.toFixed(0)}ms avg, ${requestsPerSecond.toFixed(1)} req/s`;
    });
  }

  async testBurstTraffic() {
    console.log(colorize('\n💥 Burst Traffic Test', 'cyan'));
    
    await this.runTest('Traffic burst handling', async () => {
      const burstSize = this.concurrentClients * 2;
      const startTime = performance.now();
      
      // Create a sudden burst of requests
      const promises = [];
      for (let i = 0; i < burstSize; i++) {
        promises.push(this.makeHealthRequest());
      }

      const results = await Promise.allSettled(promises);
      const totalTime = performance.now() - startTime;
      
      const successful = results.filter(r => r.status === 'fulfilled' && r.value.status === 200).length;
      const failed = results.length - successful;
      const successRate = (successful / burstSize) * 100;

      return `${successful}/${burstSize} burst requests successful (${successRate.toFixed(1)}%) in ${totalTime.toFixed(0)}ms`;
    });
  }

  async testMemoryUsage() {
    console.log(colorize('\n💾 Memory Usage Patterns', 'cyan'));
    
    await this.runTest('Large payload handling', async () => {
      const largePayload = 'x'.repeat(50000); // 50KB payload
      const iterations = 20;
      let successful = 0;

      for (let i = 0; i < iterations; i++) {
        try {
          const response = await this.makeRequest('/mcp', {
            method: 'POST',
            body: {
              jsonrpc: '2.0',
              id: this.requestId++,
              method: 'initialize',
              params: {
                protocolVersion: '2024-11-05',
                capabilities: {},
                clientInfo: { 
                  name: 'load-test', 
                  version: '1.0.0',
                  largeData: largePayload
                }
              }
            }
          });

          if (response.status === 200) {
            successful++;
          }
        } catch (error) {
          // Expected for some servers with size limits
        }
      }

      return `${successful}/${iterations} large payload requests successful`;
    });
  }

  async testConnectionLimits() {
    console.log(colorize('\n🔗 Connection Limits', 'cyan'));
    
    await this.runTest('Maximum concurrent connections', async () => {
      const maxConnections = Math.min(this.concurrentClients * 3, 100);
      const promises = [];

      // Create many concurrent connections
      for (let i = 0; i < maxConnections; i++) {
        promises.push(
          this.makeHealthRequest().catch(error => ({ error: error.message }))
        );
      }

      const results = await Promise.allSettled(promises);
      const successful = results.filter(r => 
        r.status === 'fulfilled' && 
        r.value.status === 200
      ).length;
      
      const connectionErrors = results.filter(r => 
        r.status === 'rejected' || 
        (r.status === 'fulfilled' && r.value.error)
      ).length;

      return `${successful}/${maxConnections} connections successful, ${connectionErrors} connection errors`;
    });
  }

  async makeHealthRequest() {
    return await fetch(`${this.serverUrl}/health`, {
      method: 'GET',
      headers: {
        'Accept': 'application/json'
      }
    });
  }

  async makeMCPInitializeRequest() {
    return await this.makeRequest('/mcp', {
      method: 'POST',
      body: {
        jsonrpc: '2.0',
        id: this.requestId++,
        method: 'initialize',
        params: {
          protocolVersion: '2024-11-05',
          capabilities: {},
          clientInfo: { name: 'load-test', version: '1.0.0' }
        }
      }
    });
  }

  parseSSEResponse(data) {
    const lines = data.split('\n');
    for (const line of lines) {
      if (line.startsWith('data: ')) {
        try {
          return JSON.parse(line.substring(6));
        } catch (e) {
          continue;
        }
      }
    }
    throw new Error('No valid JSON data found in SSE response');
  }

  async makeRequest(path, options = {}) {
    const url = `${this.serverUrl}${path}`;
    const method = options.method || 'GET';
    const headers = {
      'Content-Type': 'application/json',
      'Accept': 'application/json, text/event-stream',
      ...options.headers
    };
    const body = options.body;

    const response = await fetch(url, {
      method,
      headers,
      body: body ? JSON.stringify(body) : undefined
    });

    const text = await response.text();

    return {
      status: response.status,
      headers: Object.fromEntries(response.headers.entries()),
      data: text
    };
  }

  async runTest(name, testFn) {
    try {
      console.log(colorize(`  🧪 ${name}...`, 'yellow'));
      const result = await testFn();
      console.log(colorize(`  ✅ ${name}: ${result}`, 'green'));
      this.results.push({ name, status: 'PASS', message: result });
    } catch (error) {
      console.log(colorize(`  ❌ ${name}: ${error.message}`, 'red'));
      this.results.push({ name, status: 'FAIL', message: error.message });
    }
  }

  printResults() {
    console.log(colorize('\n📊 Load Test Results', 'bold'));
    console.log('='.repeat(60));

    const passed = this.results.filter(r => r.status === 'PASS').length;
    const failed = this.results.filter(r => r.status === 'FAIL').length;
    const total = this.results.length;

    console.log(colorize(`Total Tests: ${total}`, 'blue'));
    console.log(colorize(`Passed: ${passed}`, 'green'));
    console.log(colorize(`Failed: ${failed}`, failed > 0 ? 'red' : 'green'));

    const successRate = Math.round((passed / total) * 100);
    console.log(colorize(`\n🎯 Load Handling: ${successRate}%`, 
      successRate >= 90 ? 'green' : successRate >= 70 ? 'yellow' : 'red'));

    // Export results for CI/CD
    if (process.env.NODE_ENV === 'test') {
      const results = {
        timestamp: new Date().toISOString(),
        server: this.serverUrl,
        category: 'load-test',
        configuration: {
          concurrentClients: this.concurrentClients,
          testDuration: this.testDuration
        },
        summary: { total, passed, failed, successRate },
        details: this.results
      };
      
      console.log('\n📄 JSON Results:');
      console.log(JSON.stringify(results, null, 2));
    }
  }
}

// Run load tests
const serverUrl = process.env.MCP_SERVER_URL || 'http://localhost:3000';
const test = new LoadTest(serverUrl);
test.runTests().catch(console.error);
