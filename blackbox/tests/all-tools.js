#!/usr/bin/env node

/**
 * 🛠️ All Tools Test Script
 *
 * Purpose:
 * 1. Test all registered MCP tools
 * 2. Verify tool input parameters and output format
 * 3. Check error handling mechanisms
 * 4. Generate tool usage report
 */

import { SERVICE_MODULES, SERVICE_TOOL_CONFIGS } from '../dist/adapters/service-adapter.js';
import { getEnvironmentConfig } from '../dist/utils/env.js';

/**
 * 安全的JSON序列化
 */
function safeStringify(obj, space = 2) {
  const seen = new WeakSet();
  return JSON.stringify(obj, (key, value) => {
    if (typeof value === 'object' && value !== null) {
      if (seen.has(value)) {
        return '[Circular Reference]';
      }
      seen.add(value);
    }
    return value;
  }, space);
}

/**
 * Test a single tool
 */
async function testSingleTool(moduleName, toolConfig, testArgs = {}) {
  const toolName = `${moduleName}_${toolConfig.name}`;

  console.log(`\n🔧 Testing Tool: ${toolName}`);
  console.log(`📝 Description: ${toolConfig.description}`);

  const startTime = Date.now();

  try {
    const serviceModule = SERVICE_MODULES[moduleName];
    if (!serviceModule) {
      throw new Error(`Service module '${moduleName}' not found`);
    }

    const serviceFunction = serviceModule[toolConfig.name];
    if (!serviceFunction) {
      throw new Error(`Function '${toolConfig.name}' not found in module '${moduleName}'`);
    }

    console.log(`📋 Input Schema: ${toolConfig.zodSchema ? 'Defined' : 'Default'}`);
    console.log(`📋 Test Args:`, safeStringify(testArgs));

    const result = await serviceFunction(testArgs);
    const duration = Date.now() - startTime;

    console.log(`✅ Success (${duration}ms)`);
    console.log(`📊 Result Type: ${typeof result}`);

    if (result && typeof result === 'object') {
      console.log(`📊 Result Keys: ${Object.keys(result).join(', ')}`);
      if (result.status) {
        console.log(`📊 HTTP Status: ${result.status}`);
      }
      if (result.data && result.data.code) {
        console.log(`📊 Response Code: ${result.data.code}`);
      }
    }

    return {
      toolName,
      success: true,
      duration,
      result: !!result,
      error: null
    };

  } catch (error) {
    const duration = Date.now() - startTime;
    console.log(`❌ Failed (${duration}ms)`);
    console.log(`💥 Error: ${error.message}`);

    return {
      toolName,
      success: false,
      duration,
      result: false,
      error: error.message
    };
  }
}

/**
 * Get test arguments for each tool
 */
function getTestArgs(moduleName, functionName) {
  const testCases = {
    oms: {
      queryOrderLists: { page: 1, pageSize: 5 },
      getOrderDetail: { vid: '568167023' }, // Use real VID
      getLogisticsInfo: { vid: '568167023' }, // Use real VID
      getAssemblingInfo: { vid: '568167023' }, // Use real VID
      searchPermissionStore: {},
      getRetrievalData: { vid: '568167023' } // Use real VID
    },
    global: {
      getCurrentUser: {}
    },
    test: {
      ping: {},
      echo: { message: 'Test message', data: { test: true } }
    }
  };

  return testCases[moduleName]?.[functionName] || {};
}

/**
 * Main test function
 */
async function runAllToolsTest() {
  console.log('🛠️ ==================== ALL TOOLS TEST ====================');
  console.log('🎯 Purpose: Test all registered MCP tools for functionality and reliability\n');

  // Check environment configuration
  const env = getEnvironmentConfig();
  console.log('🌍 Environment Status:');
  console.log(`   Auth Cookies: ${env.AUTH_COOKIES ? '✅ Configured' : '❌ Missing'}`);
  console.log(`   Debug Mode: ${env.DEBUG_SERVICE_ADAPTER ? '✅ Enabled' : '❌ Disabled'}`);

  if (!env.AUTH_COOKIES) {
    console.log('\n⚠️  Warning: AUTH_COOKIES not configured. Some tests may fail with auth errors.');
  }
  
  const allResults = [];
  let totalTools = 0;

  // Iterate through all service modules
  for (const [moduleName, toolConfigs] of Object.entries(SERVICE_TOOL_CONFIGS)) {
    console.log(`\n📦 Testing Module: ${moduleName.toUpperCase()}`);
    console.log(`📊 Tools in module: ${toolConfigs.length}`);

    for (const toolConfig of toolConfigs) {
      const testArgs = getTestArgs(moduleName, toolConfig.name);
      const result = await testSingleTool(moduleName, toolConfig, testArgs);
      allResults.push(result);
      totalTools++;

      // Add small delay to avoid too frequent requests
      await new Promise(resolve => setTimeout(resolve, 100));
    }
  }
  
  // 生成测试报告
  console.log('\n🎯 ==================== TEST REPORT ====================');
  
  const successfulTools = allResults.filter(r => r.success && r.result);
  const failedTools = allResults.filter(r => !r.success || !r.result);
  const authErrors = allResults.filter(r => r.error && r.error.includes('Authentication'));
  
  console.log(`📊 Total Tools Tested: ${totalTools}`);
  console.log(`✅ Successful: ${successfulTools.length}`);
  console.log(`❌ Failed: ${failedTools.length}`);
  console.log(`🔐 Auth Errors: ${authErrors.length}`);
  
  // 性能统计
  const durations = allResults.map(r => r.duration);
  const avgDuration = durations.reduce((a, b) => a + b, 0) / durations.length;
  const maxDuration = Math.max(...durations);
  const minDuration = Math.min(...durations);
  
  console.log(`\n⏱️  Performance Statistics:`);
  console.log(`   Average Duration: ${avgDuration.toFixed(0)}ms`);
  console.log(`   Max Duration: ${maxDuration}ms`);
  console.log(`   Min Duration: ${minDuration}ms`);
  
  // 详细结果
  console.log(`\n📋 Detailed Results:`);
  allResults.forEach(result => {
    const status = result.success && result.result ? '✅' : '❌';
    const duration = `${result.duration}ms`;
    const error = result.error ? ` (${result.error})` : '';
    console.log(`   ${status} ${result.toolName.padEnd(30)} ${duration.padStart(6)}${error}`);
  });
  
  // 建议和总结
  console.log(`\n💡 Recommendations:`);
  
  if (authErrors.length > 0) {
    console.log(`   🔐 Update AUTH_COOKIES in .env file to fix ${authErrors.length} auth errors`);
  }
  
  if (failedTools.length > 0) {
    console.log(`   🔍 Check server logs for details on ${failedTools.length} failed tools`);
  }
  
  if (successfulTools.length === totalTools) {
    console.log(`   🎉 All tools are working perfectly!`);
  } else if (successfulTools.length > totalTools * 0.8) {
    console.log(`   👍 Most tools are working well (${Math.round(successfulTools.length / totalTools * 100)}% success rate)`);
  } else {
    console.log(`   ⚠️  Many tools are failing (${Math.round(failedTools.length / totalTools * 100)}% failure rate)`);
  }
  
  console.log('\n🏁 All tools test completed!');
  
  return {
    total: totalTools,
    successful: successfulTools.length,
    failed: failedTools.length,
    authErrors: authErrors.length,
    avgDuration,
    results: allResults
  };
}

// 运行测试
runAllToolsTest().catch(error => {
  console.error('\n💥 All tools test failed:', error.message);
  console.error('📋 Stack:', error.stack);
  process.exit(1);
});
