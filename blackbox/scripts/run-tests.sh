#!/bin/bash

# MCP Server ODI - Blackbox Test Runner
# Convenience script for running blackbox tests

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Help function
show_help() {
    echo "🧪 MCP Server ODI - Blackbox Test Runner"
    echo "========================================"
    echo ""
    echo "Usage: $0 [COMMAND]"
    echo ""
    echo "Commands:"
    echo "  fast         Run fast blackbox test (recommended)"
    echo "  full         Run complete blackbox test suite"
    echo "  clean        Clean up Docker resources"
    echo "  logs         Show Docker service logs"
    echo "  status       Show Docker service status"
    echo "  help         Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 fast      # Quick test for development"
    echo "  $0 full      # Complete test for CI/CD"
    echo "  $0 clean     # Clean up after tests"
}

# Change to project root directory
cd "$(dirname "$0")/../.."

# Main command handling
case "${1:-help}" in
    "fast")
        print_status "Running fast blackbox test..."
        make test-fast
        if [ $? -eq 0 ]; then
            print_success "Fast test completed successfully!"
        else
            print_error "Fast test failed!"
            exit 1
        fi
        ;;
    
    "full")
        print_status "Running complete blackbox test suite..."
        make test-full
        if [ $? -eq 0 ]; then
            print_success "Full test suite completed successfully!"
        else
            print_error "Full test suite failed!"
            exit 1
        fi
        ;;
    
    "clean")
        print_status "Cleaning up Docker resources..."
        make clean
        print_success "Cleanup completed!"
        ;;
    
    "logs")
        print_status "Showing Docker service logs..."
        docker-compose -f blackbox/config/docker-compose.test.yml logs
        ;;
    
    "status")
        print_status "Showing Docker service status..."
        docker-compose -f blackbox/config/docker-compose.test.yml ps
        ;;
    
    "help"|*)
        show_help
        ;;
esac
