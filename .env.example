# ================================
# 🔧 MCP Server ODI - Environment Configuration Template
# ================================
#
# ⚠️  SECURITY NOTICE:
# 1. Copy this file to .env: cp .env.example .env
# 2. Update .env with your actual values
# 3. Never commit .env to version control
# 4. Use environment-specific files for different deployments
#
# 📋 Configuration loaded in this order (later values override earlier):
# 1. Default values in code
# 2. .env file (this template)
# 3. System environment variables
# 4. Docker/container environment
#
# 🔗 Related files:
# - .env.ingka-dt (INGKA-DT environment)
# - .env.ingka-internal (INGKA-INTERNAL environment)
# - docs/ENVIRONMENT_CONFIGURATION.md (detailed guide)
# ================================

# ================================
# 🌍 Application Environment
# ================================
# Environment mode: development, production, test
NODE_ENV=production

# ================================
# 🚀 Server Configuration
# ================================
# Transport protocol: stdio (for MCP clients) or http (for web clients)
TRANSPORT=http

# Server port for HTTP transport mode (1024-65535)
MCP_SERVER_PORT=3000

# Base URL for HTTP transport (auto-generated if not set)
MCP_SERVER_BASE_URL=https://your-domain.com

# ================================
# 🔗 API Endpoints (Required)
# ================================
# Main API host for order services
VITE_API_HOST=https://fe-dev-i.ingka-dt.cn

# Kong API gateway host
VITE_API_HOST_KONG=https://api-dev-mpp-fe.ingka-dt.cn

# Master data API configuration
VITE_MASTER_DATA_API_HOST=https://master-data-api-dev.ingka-dt.cn
VITE_MASTER_DATA_API_KEY=your-master-data-api-key

# ================================
# Authentication Configuration
# ================================
# Authentication cookies (required if OAuth2 is disabled)
AUTH_COOKIES=your-auth-cookies-here

# Custom referrer header for API requests
X_CUSTOM_REFERRER=https://admin.ingka-dt.cn/app/odi/oms/index

# ================================
# OAuth2 Configuration
# ================================
# Enable/disable OAuth2 authentication
OAUTH2_ENABLED=true

# Keycloak server configuration
KEYCLOAK_BASE_URL=https://keycloak.ingka-dt.cn
KEYCLOAK_REALM=master

# OAuth2 client configuration
OAUTH2_CLIENT_ID=mcp-server
OAUTH2_CLIENT_SECRET=your-oauth2-client-secret-optional

# OAuth2 redirect URI (auto-generated if not provided)
OAUTH2_REDIRECT_URI=https://your-domain.com/auth/callback

# OAuth2 scopes (comma-separated)
OAUTH2_SCOPES=openid,profile,email

# ================================
# Connection Authentication
# ================================
# Enable authentication for MCP connections
CONNECTION_AUTH_ENABLED=true

# Strict authentication mode (reject invalid tokens)
CONNECTION_AUTH_STRICT=true

# Enable test API endpoints (disable in production)
CONNECTION_AUTH_TEST_API=false

# ================================
# Debug Configuration
# ================================
# Enable debug logging for service adapter
DEBUG_SERVICE_ADAPTER=false

# ================================
# Per-Request Authentication (Optional)
# ================================
# Enable per-request authentication (MCP compliance)
PER_REQUEST_AUTH_ENABLED=false

# Enable token caching for per-request auth
PER_REQUEST_AUTH_CACHE_ENABLED=true

# Token cache max age in seconds
PER_REQUEST_AUTH_CACHE_MAX_AGE=300

# Enable validation logging for per-request auth
PER_REQUEST_AUTH_LOG_VALIDATION=false

# ================================
# Environment-Specific Examples
# ================================

# Development Environment Example:
# NODE_ENV=development
# TRANSPORT=stdio
# OAUTH2_ENABLED=false
# CONNECTION_AUTH_ENABLED=false
# DEBUG_SERVICE_ADAPTER=true

# Test Environment Example:
# NODE_ENV=test
# MCP_SERVER_BASE_URL=https://mcp-test.ingka-dt.cn
# CONNECTION_AUTH_TEST_API=true
# VITE_API_HOST=https://fe-test-i.ingka-dt.cn

# Production Environment Example:
# NODE_ENV=production
# MCP_SERVER_BASE_URL=https://mcp.ingka-internal.cn
# OAUTH2_ENABLED=true
# CONNECTION_AUTH_ENABLED=true
# CONNECTION_AUTH_STRICT=true
# CONNECTION_AUTH_TEST_API=false
# DEBUG_SERVICE_ADAPTER=false

# ================================
# Security Notes
# ================================
# 1. Never commit .env files with real secrets to version control
# 2. Use strong, unique values for OAUTH2_CLIENT_SECRET
# 3. Ensure KEYCLOAK_BASE_URL uses HTTPS in production
# 4. Set CONNECTION_AUTH_STRICT=true in production
# 5. Disable DEBUG_SERVICE_ADAPTER in production
# 6. Use internal domains for MCP_SERVER_BASE_URL when possible

# ================================
# Validation Notes
# ================================
# Required when OAUTH2_ENABLED=true:
# - OAUTH2_CLIENT_ID
# - KEYCLOAK_BASE_URL
# - KEYCLOAK_REALM
# Optional:
# - OAUTH2_CLIENT_SECRET (for confidential clients)
#
# Required when OAUTH2_ENABLED=false:
# - AUTH_COOKIES
#
# Always required:
# - VITE_API_HOST_KONG
