/**
 * NACOS Configuration Client
 * 
 * Integrates with Alibaba Cloud MSE (Microservices Engine) NACOS
 * for centralized configuration management, following the same pattern
 * as other enterprise services.
 */

import { NacosConfigClient } from 'nacos';

interface NacosConfig {
  // API Configuration
  VITE_API_HOST?: string;
  VITE_API_HOST_KONG?: string;
  VITE_MASTER_DATA_API_HOST?: string;
  VITE_MASTER_DATA_API_KEY?: string;
  X_CUSTOM_REFERRER?: string;
  
  // Authentication Configuration
  AUTH_COOKIES?: string;
  OAUTH2_ENABLED?: boolean;
  OAUTH2_CLIENT_ID?: string;
  OAUTH2_CLIENT_SECRET?: string;
  KEYCLOAK_BASE_URL?: string;
  KEYCLOAK_REALM?: string;
  
  // Application Configuration
  DEBUG_SERVICE_ADAPTER?: boolean;
  CONNECTION_AUTH_ENABLED?: boolean;
  CONNECTION_AUTH_STRICT?: boolean;
  CONNECTION_AUTH_TEST_API?: boolean;
  
  // Feature Flags
  FEATURES?: {
    [key: string]: boolean;
  };
}

class NacosConfigManager {
  private client: NacosConfigClient | null = null;
  private config: NacosConfig = {};
  private initialized = false;
  private readonly dataId = 'mcp-server-odi';
  private readonly group = 'DEFAULT_GROUP';

  constructor() {
    this.initializeClient();
  }

  private initializeClient() {
    try {
      // Check if NACOS configuration is available
      const serverAddr = process.env.NACOS_SERVER_ADDR;
      const namespace = process.env.NACOS_NAMESPACE;
      const accessKey = process.env.NACOS_ACCESS_KEY;
      const secretKey = process.env.NACOS_SECRET_KEY;

      if (!serverAddr || !namespace || !accessKey || !secretKey) {
        console.warn('🔧 NACOS configuration not available, using environment variables only');
        return;
      }

      this.client = new NacosConfigClient({
        serverAddr,
        namespace,
        accessKey,
        secretKey,
      });

      console.log('🔧 NACOS client initialized successfully');
    } catch (error) {
      console.error('❌ Failed to initialize NACOS client:', error);
    }
  }

  /**
   * Load configuration from NACOS
   */
  async loadConfig(): Promise<NacosConfig> {
    if (!this.client) {
      console.warn('🔧 NACOS client not available, returning empty config');
      return {};
    }

    try {
      console.log('📋 Loading configuration from NACOS...');
      const configContent = await this.client.getConfig(this.dataId, this.group);
      
      if (!configContent) {
        console.warn('⚠️  No configuration found in NACOS for', this.dataId);
        return {};
      }

      const parsedConfig = JSON.parse(configContent);
      this.config = parsedConfig;
      this.initialized = true;
      
      console.log('✅ Configuration loaded from NACOS successfully');
      console.log('📊 Loaded config keys:', Object.keys(parsedConfig));
      
      return parsedConfig;
    } catch (error) {
      console.error('❌ Failed to load configuration from NACOS:', error);
      return {};
    }
  }

  /**
   * Get a specific configuration value
   */
  getConfig<T = any>(key: keyof NacosConfig, defaultValue?: T): T {
    const value = this.config[key];
    return value !== undefined ? (value as T) : (defaultValue as T);
  }

  /**
   * Get all configuration
   */
  getAllConfig(): NacosConfig {
    return { ...this.config };
  }

  /**
   * Check if NACOS is initialized and working
   */
  isInitialized(): boolean {
    return this.initialized;
  }

  /**
   * Subscribe to configuration changes (optional feature)
   */
  async subscribeToChanges(callback: (config: NacosConfig) => void): Promise<void> {
    if (!this.client) {
      console.warn('🔧 NACOS client not available, cannot subscribe to changes');
      return;
    }

    try {
      await this.client.subscribe({
        dataId: this.dataId,
        group: this.group,
      }, (content: string) => {
        try {
          const newConfig = JSON.parse(content);
          this.config = newConfig;
          console.log('🔄 Configuration updated from NACOS');
          callback(newConfig);
        } catch (error) {
          console.error('❌ Failed to parse updated configuration:', error);
        }
      });
      
      console.log('🔔 Subscribed to NACOS configuration changes');
    } catch (error) {
      console.error('❌ Failed to subscribe to NACOS changes:', error);
    }
  }

  /**
   * Publish configuration to NACOS (for testing/admin purposes)
   */
  async publishConfig(config: NacosConfig): Promise<boolean> {
    if (!this.client) {
      console.warn('🔧 NACOS client not available, cannot publish config');
      return false;
    }

    try {
      const configContent = JSON.stringify(config, null, 2);
      const result = await this.client.publishConfig(this.dataId, this.group, configContent);
      
      if (result) {
        console.log('✅ Configuration published to NACOS successfully');
        this.config = config;
      } else {
        console.error('❌ Failed to publish configuration to NACOS');
      }
      
      return result;
    } catch (error) {
      console.error('❌ Error publishing configuration to NACOS:', error);
      return false;
    }
  }
}

// Singleton instance
const nacosConfigManager = new NacosConfigManager();

export { nacosConfigManager, NacosConfig };
export default nacosConfigManager;
