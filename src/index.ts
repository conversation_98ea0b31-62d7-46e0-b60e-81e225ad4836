#!/usr/bin/env node
import { StdioServerTransport } from "@modelcontextprotocol/sdk/server/stdio.js";
import { createServer } from "./server.js";
import { initializeOAuth2 } from "./auth/index.js";
import { getEnvironmentConfig, getEnvironmentConfigWithNacos, logEnvironmentInfo, validateEnvironment } from "./utils/env.js";
import { HttpMCPTransport } from "./transport/http-transport.js";

async function main() {
  // Log environment information
  logEnvironmentInfo();

  // Validate environment
  const envValidation = validateEnvironment();

  // Show warnings and security issues
  if (envValidation.warnings.length > 0) {
    console.error('⚠️  Environment warnings:');
    envValidation.warnings.forEach(warning => console.error(`  - ${warning}`));
  }

  if (envValidation.security.length > 0) {
    console.error('🔒 Security recommendations:');
    envValidation.security.forEach(security => console.error(`  - ${security}`));
  }

  // Exit on missing required variables
  if (!envValidation.valid) {
    console.error('❌ Environment validation failed - missing required variables:');
    envValidation.missing.forEach(missing => console.error(`  - ${missing}`));
    console.error('💡 Tip: Copy .env.example to .env and update with your values');
    process.exit(1);
  }

  // OAuth2 validation is now handled during initialization

  // Create MCP server
  const server = createServer();

  // Initialize OAuth2 (if enabled) - provider will be used by HTTP transport
  // Load configuration with NACOS integration
  const env = await getEnvironmentConfigWithNacos();
  let oauth2Provider;

  if (env.OAUTH2_ENABLED) {
    console.error('🔐 OAuth2 is enabled, initializing provider...');
    try {
      const oauth2Components = await initializeOAuth2(server);
      oauth2Provider = oauth2Components.provider;
    } catch (error) {
      console.error('❌ Failed to initialize OAuth2:', error);
      console.error('💡 Tip: For MCP Inspector, set OAUTH2_ENABLED=false in your .env file');
      console.error('💡 OAuth2 is only needed for HTTP API access, not stdio MCP protocol');
      process.exit(1);
    }
  } else {
    console.error('🔐 OAuth2 is disabled, using legacy authentication');
  }

  // Determine transport type
  const transportType = process.env.TRANSPORT || 'stdio';

  if (transportType === 'http') {
    // HTTP transport mode with official StreamableHTTPServerTransport
    console.error('🌐 Starting Streamable HTTP transport mode...');
    const httpTransport = new HttpMCPTransport(env.MCP_SERVER_PORT, oauth2Provider);

    // Set the MCP server instance for the transport
    httpTransport.setMCPServer(server);

    // Start the HTTP server
    await httpTransport.start();

    console.error(`✅ orders-portal-mcp-server running on Streamable HTTP transport (port ${env.MCP_SERVER_PORT})`);
    console.error(`📡 Session-based MCP communication with Server-Sent Events`);

    if (env.OAUTH2_ENABLED && oauth2Provider) {
      console.error(`🔐 OAuth2 endpoints integrated with HTTP transport`);
      console.error(`📋 OAuth2 metadata: ${env.MCP_SERVER_BASE_URL}/.well-known/oauth-protected-resource`);
    }
  } else {
    // Default stdio transport mode
    console.error('📡 Starting stdio transport mode...');
    const transport = new StdioServerTransport();
    await server.connect(transport);
    console.error("✅ orders-portal-mcp-server running on stdio");

    if (env.OAUTH2_ENABLED) {
      console.error(`🔐 OAuth2 HTTP server available at: ${env.MCP_SERVER_BASE_URL}`);
      console.error(`📋 OAuth2 metadata: ${env.MCP_SERVER_BASE_URL}/.well-known/oauth-authorization-server`);
    }
  }
}

main().catch((error) => {
  console.error("Fatal error in main():", error);
  process.exit(1);
});
