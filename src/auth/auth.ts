/**
 * 🔐 Unified Authentication Module
 * 
 * Consolidates all authentication logic:
 * - OAuth2 token validation (connection + per-request)
 * - <PERSON>ie authentication
 * - Token caching
 * - MCP compliance
 */

import { Request, Response, NextFunction } from 'express';
import { ProxyOAuthServerProvider } from '@modelcontextprotocol/sdk/server/auth/providers/proxyProvider.js';
import { AuthInfo } from '@modelcontextprotocol/sdk/server/auth/types.js';
import { getEnvironmentConfig } from '../utils/env.js';

export interface AuthConfig {
  // Connection auth (initialize requests)
  connectionAuthEnabled: boolean;
  connectionAuthStrict: boolean;
  connectionAuthTestApi: boolean;
  
  // Per-request auth (MCP compliance)
  perRequestAuthEnabled: boolean;
  
  // Token caching
  cacheEnabled: boolean;
  cacheMaxAge: number;
  
  // Debugging
  logValidation: boolean;
}

export interface AuthResult {
  success: boolean;
  authInfo?: AuthInfo;
  error?: string;
  cached?: boolean;
  duration?: number;
}

/**
 * Simple token cache with expiration
 */
class TokenCache {
  private cache = new Map<string, { authInfo: AuthInfo; expiresAt: number }>();
  private maxAge: number;

  constructor(maxAgeSeconds: number = 300) {
    this.maxAge = maxAgeSeconds * 1000;
  }

  get(token: string): AuthInfo | null {
    const entry = this.cache.get(token);
    if (!entry || Date.now() > entry.expiresAt) {
      this.cache.delete(token);
      return null;
    }
    return entry.authInfo;
  }

  set(token: string, authInfo: AuthInfo): void {
    this.cache.set(token, {
      authInfo,
      expiresAt: Date.now() + this.maxAge
    });
  }

  clear(): void { this.cache.clear(); }
  size(): number { return this.cache.size; }
}

const tokenCache = new TokenCache();

/**
 * Load unified auth configuration
 */
export function loadAuthConfig(): AuthConfig {
  const env = getEnvironmentConfig();
  return {
    connectionAuthEnabled: env.CONNECTION_AUTH_ENABLED,
    connectionAuthStrict: env.CONNECTION_AUTH_STRICT,
    connectionAuthTestApi: env.CONNECTION_AUTH_TEST_API,
    perRequestAuthEnabled: process.env.PER_REQUEST_AUTH_ENABLED === 'true',
    cacheEnabled: process.env.PER_REQUEST_AUTH_CACHE_ENABLED !== 'false',
    cacheMaxAge: parseInt(process.env.PER_REQUEST_AUTH_CACHE_MAX_AGE || '300'),
    logValidation: process.env.PER_REQUEST_AUTH_LOG_VALIDATION === 'true'
  };
}

/**
 * Validate OAuth2 Bearer token
 */
export async function validateToken(
  req: Request,
  oauth2Provider?: ProxyOAuthServerProvider
): Promise<AuthResult> {
  const startTime = Date.now();
  const config = loadAuthConfig();

  try {
    // Extract Bearer token
    const authHeader = req.headers.authorization;
    if (!authHeader?.startsWith('Bearer ')) {
      return {
        success: false,
        error: 'Missing or invalid Authorization header',
        duration: Date.now() - startTime
      };
    }

    const token = authHeader.substring(7);
    
    // Check cache first
    if (config.cacheEnabled) {
      const cachedAuth = tokenCache.get(token);
      if (cachedAuth) {
        if (config.logValidation) {
          console.log('🎯 [Auth] Cache hit');
        }
        return {
          success: true,
          authInfo: cachedAuth,
          cached: true,
          duration: Date.now() - startTime
        };
      }
    }

    // Validate with OAuth2 provider
    if (!oauth2Provider) {
      return {
        success: false,
        error: 'OAuth2 provider not available',
        duration: Date.now() - startTime
      };
    }

    const authInfo = await oauth2Provider.verifyAccessToken(token);
    
    // Cache the result
    if (config.cacheEnabled) {
      tokenCache.set(token, authInfo);
    }

    if (config.logValidation) {
      console.log('✅ [Auth] Token validation successful:', {
        clientId: authInfo.clientId,
        scopes: authInfo.scopes?.slice(0, 3),
        duration: Date.now() - startTime
      });
    }

    return {
      success: true,
      authInfo,
      cached: false,
      duration: Date.now() - startTime
    };

  } catch (error) {
    const duration = Date.now() - startTime;
    if (config.logValidation) {
      console.log('❌ [Auth] Token validation failed:', error instanceof Error ? error.message : 'Unknown error');
    }
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Token validation failed',
      duration
    };
  }
}

/**
 * Validate cookie authentication (fallback)
 */
export async function validateCookie(req: Request): Promise<AuthResult> {
  const startTime = Date.now();
  
  try {
    const cookies = req.headers.cookie;
    if (!cookies) {
      return {
        success: false,
        error: 'No authentication cookies provided',
        duration: Date.now() - startTime
      };
    }

    const ordersCookieMatch = cookies.match(/test_orders-portal=([^;]+)/);
    if (!ordersCookieMatch) {
      return {
        success: false,
        error: 'Missing test_orders-portal cookie',
        duration: Date.now() - startTime
      };
    }

    const pmUserToken = ordersCookieMatch[1];
    
    // Basic token format validation
    try {
      const tokenString = Buffer.from(pmUserToken, 'base64').toString();
      const parts = tokenString.split('@');
      
      if (parts.length !== 2) {
        throw new Error('Invalid token format');
      }

      const [ssoUUID, rightPart] = parts;
      const rightParts = rightPart.split('.');
      
      if (rightParts.length < 3) {
        throw new Error('Invalid token structure');
      }

      const loginTime = parseInt(rightParts[0]);
      const userId = rightParts[1];
      const sessionTimeout = parseInt(rightParts[rightParts.length - 1]);
      
      // Check expiration
      const expiresAt = new Date(loginTime + (sessionTimeout * 1000));
      if (new Date() > expiresAt) {
        return {
          success: false,
          error: `Token expired at ${expiresAt.toISOString()}`,
          duration: Date.now() - startTime
        };
      }

      return {
        success: true,
        authInfo: {
          token: pmUserToken,
          clientId: 'cookie-auth',
          scopes: ['profile'],
          expiresAt: Math.floor(expiresAt.getTime() / 1000),
          extra: { userInfo: { userId, ssoUUID } }
        } as AuthInfo,
        duration: Date.now() - startTime
      };

    } catch (error) {
      return {
        success: false,
        error: `Invalid token format: ${error instanceof Error ? error.message : 'Unknown error'}`,
        duration: Date.now() - startTime
      };
    }

  } catch (error) {
    return {
      success: false,
      error: `Cookie validation failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
      duration: Date.now() - startTime
    };
  }
}

/**
 * Main authentication function - tries OAuth2 first, then cookie fallback
 */
export async function authenticate(
  req: Request,
  oauth2Provider?: ProxyOAuthServerProvider
): Promise<AuthResult> {
  const config = loadAuthConfig();
  
  // Try OAuth2 first
  if (req.headers.authorization) {
    const oauth2Result = await validateToken(req, oauth2Provider);
    if (oauth2Result.success || config.connectionAuthStrict) {
      return oauth2Result;
    }
  }
  
  // Fallback to cookie auth
  if (req.headers.cookie) {
    return await validateCookie(req);
  }
  
  return {
    success: false,
    error: 'No valid authentication provided. Please provide OAuth2 Bearer token or authentication cookies.'
  };
}

/**
 * Express middleware for per-request authentication (MCP compliance)
 */
export function createAuthMiddleware(oauth2Provider?: ProxyOAuthServerProvider) {
  const config = loadAuthConfig();

  return async (req: Request, res: Response, next: NextFunction) => {
    // Skip if disabled or not MCP endpoint
    if (!config.perRequestAuthEnabled || !req.path.startsWith('/mcp')) {
      return next();
    }

    // Skip health and metadata endpoints
    if (['/health', '/.well-known', '/auth/status', '/mcp/capabilities'].some(path => req.path.startsWith(path))) {
      return next();
    }

    const authResult = await authenticate(req, oauth2Provider);

    if (!authResult.success) {
      const errorType = authResult.error?.includes('Missing required scopes') ? 'insufficient_scope' : 'invalid_token';
      const statusCode = errorType === 'insufficient_scope' ? 403 : 401;
      
      res.set('WWW-Authenticate', `Bearer realm="MCP Server", error="${errorType}", error_description="${authResult.error}"`);
      
      return res.status(statusCode).json({
        error: errorType,
        error_description: authResult.error,
        error_uri: 'https://tools.ietf.org/html/rfc6750#section-3.1'
      });
    }

    // Attach auth info to request
    (req as any).auth = authResult.authInfo;
    (req as any).authMeta = {
      cached: authResult.cached,
      duration: authResult.duration
    };

    next();
  };
}

/**
 * Get authentication status and metrics
 */
export function getAuthStatus() {
  const config = loadAuthConfig();
  return {
    config,
    cache: {
      enabled: config.cacheEnabled,
      size: tokenCache.size(),
      maxAge: config.cacheMaxAge
    },
    timestamp: new Date().toISOString()
  };
}

/**
 * Clear token cache
 */
export function clearAuthCache() {
  tokenCache.clear();
}
