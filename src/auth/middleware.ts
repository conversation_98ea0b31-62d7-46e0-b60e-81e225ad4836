import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from 'express';
import { requireBearerAuth } from '@modelcontextprotocol/sdk/server/auth/middleware/bearerAuth.js';
import { ProxyOAuthServerProvider } from '@modelcontextprotocol/sdk/server/auth/providers/proxyProvider.js';

/**
 * Scope validation mode
 */
export type ScopeValidationMode = 'all' | 'any';

/**
 * Enhanced OAuth2 middleware options
 */
export interface OAuth2MiddlewareOptions {
  /** Required scopes for access */
  requiredScopes?: string[];
  /** Resource metadata URL for WWW-Authenticate header */
  resourceMetadataUrl?: string;
  /** Scope validation mode: 'all' requires all scopes, 'any' requires at least one */
  scopeValidation?: ScopeValidationMode;
  /** Custom scope validator function */
  customScopeValidator?: (userScopes: string[], requiredScopes: string[]) => boolean;
  /** Enable detailed logging */
  enableLogging?: boolean;
}

/**
 * Create OAuth2 authentication middleware with enhanced scope validation
 */
export function createOAuth2Middleware(
  provider: ProxyOAuthServerProvider,
  options: OAuth2MiddlewareOptions = {}
): RequestHandler {
  const {
    requiredScopes = [],
    resourceMetadataUrl,
    scopeValidation = 'all',
    customScopeValidator,
    enableLogging = false
  } = options;

  return requireBearerAuth({
    verifier: provider,
    requiredScopes: requiredScopes,
    resourceMetadataUrl: resourceMetadataUrl,
    // Override scope validation if custom logic is needed
    ...(customScopeValidator || scopeValidation !== 'all' ? {
      customScopeValidator: (userScopes: string[], required: string[]) => {
        if (enableLogging) {
          console.log(`🔍 [OAuth2Middleware] Validating scopes:`, {
            userScopes,
            requiredScopes: required,
            validationMode: scopeValidation
          });
        }

        let result: boolean;
        let clientId = 'unknown';

        // Extract client ID from user scopes if available
        const clientScope = userScopes.find(scope => scope.startsWith('client:'));
        if (clientScope) {
          clientId = clientScope.replace('client:', '');
        }

        // Use custom validator if provided
        if (customScopeValidator) {
          result = customScopeValidator(userScopes, required);
          if (enableLogging) {
            console.log(`🎯 [OAuth2Middleware] Custom validation result: ${result}`);
          }
        } else if (scopeValidation === 'any') {
          // Built-in 'any' validation
          result = required.length === 0 || required.some(scope => userScopes.includes(scope));
          if (enableLogging) {
            console.log(`🎯 [OAuth2Middleware] 'any' validation result: ${result}`);
          }
        } else {
          // Default 'all' validation
          result = required.every(scope => userScopes.includes(scope));
          if (enableLogging) {
            console.log(`🎯 [OAuth2Middleware] 'all' validation result: ${result}`);
          }
        }

        // Log scope validation result
        import('./oauth-logger.js').then(({ globalOAuth2Logger }) => {
          globalOAuth2Logger.logScopeValidation(
            clientId,
            userScopes,
            required,
            scopeValidation,
            result
          );
        });

        return result;
      }
    } : {})
  });
}

// Note: Custom middleware functions removed - using MCP SDK's requireBearerAuth instead
// The MCP SDK provides standardized authentication middleware that handles:
// - Bearer token validation
// - Proper WWW-Authenticate headers
// - OAuth2 error responses
// - Resource metadata integration

/**
 * Create middleware for read-only access (requires read scopes)
 */
export function requireReadAccess(
  provider: ProxyOAuthServerProvider,
  options: Omit<OAuth2MiddlewareOptions, 'requiredScopes'> = {}
): RequestHandler {
  return createOAuth2Middleware(provider, {
    ...options,
    requiredScopes: ['read', 'openid'],
    scopeValidation: 'all'
  });
}

/**
 * Create middleware for admin access (requires admin scopes)
 */
export function requireAdminAccess(
  provider: ProxyOAuthServerProvider,
  options: Omit<OAuth2MiddlewareOptions, 'requiredScopes'> = {}
): RequestHandler {
  return createOAuth2Middleware(provider, {
    ...options,
    requiredScopes: ['admin', 'openid'],
    scopeValidation: 'all'
  });
}
