import { getEnvironmentConfig } from '../utils/env.js';

/**
 * OAuth2 Configuration for Keycloak Integration
 */
export interface OAuth2Config {
  enabled: boolean;
  keycloak: {
    baseUrl: string;
    realm: string;
    authUrl: string;
    tokenUrl: string;
    userInfoUrl: string;
    jwksUrl: string;
    revocationUrl: string;
    registrationUrl: string;
    issuer: string;
  };
  client: {
    id: string;
    secret: string;
    redirectUri: string;
    scopes: string[];
  };
  server: {
    baseUrl: string;
    port: number;
    issuerUrl: string;
  };
}

/**
 * Get OAuth2 configuration from environment variables
 */
export function getOAuth2Config(): OAuth2Config {
  const env = getEnvironmentConfig();
  
  const keycloakBaseUrl = env.KEYCLOAK_BASE_URL;
  const realm = env.KEYCLOAK_REALM;
  const realmUrl = `${keycloakBaseUrl}/realms/${realm}`;
  
  return {
    enabled: env.OAUTH2_ENABLED,
    keycloak: {
      baseUrl: keycloakBaseUrl,
      realm: realm,
      authUrl: `${realmUrl}/protocol/openid-connect/auth`,
      tokenUrl: `${realmUrl}/protocol/openid-connect/token`,
      userInfoUrl: `${realmUrl}/protocol/openid-connect/userinfo`,
      jwksUrl: `${realmUrl}/protocol/openid-connect/certs`,
      revocationUrl: `${realmUrl}/protocol/openid-connect/revoke`,
      registrationUrl: `${realmUrl}/clients-registrations/openid-connect`,
      issuer: realmUrl,
    },
    client: {
      id: env.OAUTH2_CLIENT_ID,
      secret: env.OAUTH2_CLIENT_SECRET,
      redirectUri: env.OAUTH2_REDIRECT_URI,
      scopes: env.OAUTH2_SCOPES,
    },
    server: {
      baseUrl: env.MCP_SERVER_BASE_URL,
      port: env.MCP_SERVER_PORT,
      issuerUrl: env.OAUTH2_ISSUER_URL,
    },
  };
}

/**
 * Validate OAuth2 configuration
 */
export function validateOAuth2Config(config: OAuth2Config): { valid: boolean; errors: string[] } {
  const errors: string[] = [];
  
  if (!config.enabled) {
    return { valid: true, errors: [] };
  }
  
  // Validate Keycloak configuration
  if (!config.keycloak.baseUrl) {
    errors.push('Keycloak base URL is required');
  }
  
  if (!config.keycloak.realm) {
    errors.push('Keycloak realm is required');
  }
  
  // Validate client configuration
  if (!config.client.id) {
    errors.push('OAuth2 client ID is required');
  }

  // Note: client secret is optional for public clients
  
  if (!config.client.redirectUri) {
    errors.push('OAuth2 redirect URI is required');
  }
  
  if (!config.client.scopes || config.client.scopes.length === 0) {
    errors.push('OAuth2 scopes are required');
  }
  
  // Validate server configuration
  if (!config.server.baseUrl) {
    errors.push('MCP server base URL is required');
  }
  
  if (!config.server.port || config.server.port <= 0) {
    errors.push('MCP server port must be a positive number');
  }
  
  return {
    valid: errors.length === 0,
    errors,
  };
}

/**
 * Log OAuth2 configuration (hiding sensitive information)
 */
export function logOAuth2Config(config: OAuth2Config) {
  console.error('🔐 OAuth2 Configuration:');
  console.error(`  Enabled: ${config.enabled}`);
  
  if (!config.enabled) {
    console.error('  OAuth2 is disabled');
    return;
  }
  
  console.error('  Keycloak:');
  console.error(`    Base URL: ${config.keycloak.baseUrl}`);
  console.error(`    Realm: ${config.keycloak.realm}`);
  console.error(`    Issuer: ${config.keycloak.issuer}`);
  console.error(`    Auth URL: ${config.keycloak.authUrl}`);
  console.error(`    Token URL: ${config.keycloak.tokenUrl}`);
  console.error(`    JWKS URL: ${config.keycloak.jwksUrl}`);
  
  console.error('  Client:');
  console.error(`    ID: ${config.client.id}`);
  console.error(`    Secret: ${config.client.secret ? '***SET***' : '***NOT SET***'}`);
  console.error(`    Redirect URI: ${config.client.redirectUri}`);
  console.error(`    Scopes: ${config.client.scopes.join(', ')}`);
  
  console.error('  Server:');
  console.error(`    Base URL: ${config.server.baseUrl}`);
  console.error(`    Port: ${config.server.port}`);
  console.error(`    Issuer URL: ${config.server.issuerUrl}`);
}

/**
 * Get OAuth2 endpoints for Keycloak
 */
export function getKeycloakEndpoints(config: OAuth2Config) {
  return {
    authorizationUrl: config.keycloak.authUrl,
    tokenUrl: config.keycloak.tokenUrl,
    revocationUrl: config.keycloak.revocationUrl,
    registrationUrl: config.keycloak.registrationUrl,
  };
}
