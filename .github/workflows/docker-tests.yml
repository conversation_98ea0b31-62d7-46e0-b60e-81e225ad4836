name: 🐳 Docker Test Suite

on:
  push:
    branches: [ main, master, develop ]
  pull_request:
    branches: [ main, master, develop ]
  schedule:
    # Run tests daily at 2 AM UTC
    - cron: '0 2 * * *'
  workflow_dispatch:
    inputs:
      test_type:
        description: 'Type of tests to run'
        required: true
        default: 'fast'
        type: choice
        options:
          - fast
          - full
          - oauth2-disabled
          - oauth2-enabled
          - client-simulation
          - performance

env:
  DOCKER_BUILDKIT: 1
  COMPOSE_DOCKER_CLI_BUILD: 1

jobs:
  # Fast parallel tests (default)
  fast-tests:
    name: ⚡ Fast Parallel Tests
    runs-on: ubuntu-latest
    if: github.event.inputs.test_type == 'fast' || github.event.inputs.test_type == '' || github.event_name != 'workflow_dispatch'
    timeout-minutes: 15
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 🐳 Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: 🔧 Create test results directory
        run: mkdir -p test-results

      - name: ⚡ Run fast parallel tests
        run: make test-fast

      - name: 📊 Upload test results
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: fast-test-results
          path: test-results/
          retention-days: 7

      - name: 📋 Display test summary
        if: always()
        run: |
          echo "## 📊 Test Results Summary" >> $GITHUB_STEP_SUMMARY
          if [ -f test-results/docker-test-report-*.json ]; then
            LATEST_REPORT=$(ls -t test-results/docker-test-report-*.json | head -1)
            echo "### Results from: $(basename $LATEST_REPORT)" >> $GITHUB_STEP_SUMMARY
            echo '```json' >> $GITHUB_STEP_SUMMARY
            cat $LATEST_REPORT | jq '.summary' >> $GITHUB_STEP_SUMMARY
            echo '```' >> $GITHUB_STEP_SUMMARY
          fi

  # Full test suite
  full-tests:
    name: 🧪 Full Test Suite
    runs-on: ubuntu-latest
    if: github.event.inputs.test_type == 'full'
    timeout-minutes: 30
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 🐳 Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: 🧪 Run full test suite
        run: make test-full

      - name: 📊 Upload test results
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: full-test-results
          path: test-results/
          retention-days: 30

  # OAuth2 specific tests
  oauth2-tests:
    name: 🔐 OAuth2 Tests
    runs-on: ubuntu-latest
    if: contains(github.event.inputs.test_type, 'oauth2')
    timeout-minutes: 20
    strategy:
      matrix:
        auth_type: [disabled, enabled]
        include:
          - auth_type: disabled
            test_command: test-oauth2-disabled
          - auth_type: enabled
            test_command: test-oauth2-enabled
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 🐳 Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: 🔐 Run OAuth2 ${{ matrix.auth_type }} tests
        run: make ${{ matrix.test_command }}

      - name: 📊 Upload test results
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: oauth2-${{ matrix.auth_type }}-results
          path: test-results/
          retention-days: 7

  # MCP Client Simulation
  client-simulation:
    name: 🤖 MCP Client Simulation
    runs-on: ubuntu-latest
    if: github.event.inputs.test_type == 'client-simulation'
    timeout-minutes: 10
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 🐳 Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: 🤖 Run MCP client simulation
        run: make test-client

      - name: 📊 Upload test results
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: client-simulation-results
          path: test-results/
          retention-days: 7

  # Performance Tests
  performance-tests:
    name: ⚡ Performance Tests
    runs-on: ubuntu-latest
    if: github.event.inputs.test_type == 'performance'
    timeout-minutes: 15
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 🐳 Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: ⚡ Run performance tests
        run: make test-performance

      - name: 📊 Upload test results
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: performance-test-results
          path: test-results/
          retention-days: 7

  # Security scan
  security-scan:
    name: 🔒 Security Scan
    runs-on: ubuntu-latest
    if: github.event_name == 'push' || github.event_name == 'schedule'
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 🐳 Build production image for security scan
        run: docker build -f Dockerfile -t mcp-server-odi:security-scan . --target production

      - name: 🔒 Run Trivy vulnerability scanner
        uses: aquasecurity/trivy-action@master
        with:
          image-ref: 'mcp-server-odi:security-scan'
          format: 'sarif'
          output: 'trivy-results.sarif'

      - name: 📊 Upload Trivy scan results
        uses: github/codeql-action/upload-sarif@v3
        if: always()
        with:
          sarif_file: 'trivy-results.sarif'

  # Cleanup
  cleanup:
    name: 🧹 Cleanup
    runs-on: ubuntu-latest
    if: always()
    needs: [fast-tests, full-tests, oauth2-tests, client-simulation, performance-tests]
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 🧹 Clean up Docker resources
        run: |
          docker system prune -af --volumes || true
          docker network prune -f || true
