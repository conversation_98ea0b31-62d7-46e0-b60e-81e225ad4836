{"name": "mcp-server-odi", "version": "0.1.0", "type": "module", "bin": {"mcp-server-odi": "dist/index.js"}, "scripts": {"build": "tsc && shx chmod +x dist/index.js", "watch": "tsc --watch", "start": "node ./dist/index.js", "dev": "tsx watch src/index.ts", "test": "echo 'Use: npm run test:unit for unit tests, or make test-fast/test-full for integration tests'", "nacos:validate": "node scripts/nacos-config-manager.js validate", "nacos:test": "node scripts/nacos-config-manager.js test-connection", "test:unit": "echo 'No unit tests implemented yet. Add Jest/Mocha tests here.'", "test:lint": "echo 'Add ESLint/TypeScript checking here'", "inspect": "npx @modelcontextprotocol/inspector tsx src/index.ts", "validate:env": "node scripts/validate-env.js", "config:oauth2-enabled": "node scripts/switch-auth-config.js --oauth2-enabled", "config:oauth2-disabled": "node scripts/switch-auth-config.js --oauth2-disabled", "config:status": "node scripts/switch-auth-config.js --status", "prestart": "npm run validate:env", "predev": "npm run validate:env"}, "files": ["dist"], "keywords": ["mcp"], "repository": {"type": "git", "url": ""}, "author": "", "license": "MIT", "description": "mcp-server-odi", "dependencies": {"@modelcontextprotocol/sdk": "^1.16.0", "@types/node-jose": "^1.1.13", "axios": "^1.6.0", "dotenv": "^16.3.0", "eventsource": "^2.0.2", "express": "^5.1.0", "express-rate-limit": "^8.0.1", "nacos": "^2.6.0", "ws": "^8.18.0", "zod": "^3.25.76", "zod-to-json-schema": "^3.24.6"}, "devDependencies": {"@modelcontextprotocol/inspector": "^0.16.1", "@types/express": "^5.0.1", "@types/node": "^20.0.0", "shx": "^0.3.4", "tsx": "^4.0.0", "typescript": "^5.0.0"}}