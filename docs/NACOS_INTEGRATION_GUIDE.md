# 🔧 NACOS Integration Guide

Complete guide for integrating and managing NACOS configuration with the MCP Server ODI.

## 📋 Overview

The MCP Server ODI integrates with Alibaba Cloud MSE (Microservices Engine) NACOS for centralized configuration management, following the same enterprise pattern used by other services in the organization.

## 🏗️ Architecture

### Configuration Layers

```
┌─────────────────────────────────────────────────────────────┐
│                    Configuration Hierarchy                   │
├─────────────────────────────────────────────────────────────┤
│ 1. Default Values (in code)                                │
│ 2. NACOS Configuration (centralized, dynamic)              │
│ 3. Environment Variables (infrastructure-level)            │
│ 4. Kubernetes Secrets (sensitive data)                     │
└─────────────────────────────────────────────────────────────┘
```

### Integration Flow

```
┌──────────────┐    ┌─────────────┐    ┌─────────────────┐
│ MCP Server   │───▶│ NACOS Client│───▶│ MSE/NACOS       │
│ Startup      │    │ (Node.js)   │    │ Configuration   │
└──────────────┘    └─────────────┘    │ Store           │
                                       └─────────────────┘
```

## 🚀 Quick Start

### 1. Prerequisites

- NACOS server access credentials
- Kubernetes secrets configured
- Node.js NACOS client installed (`npm install nacos`)

### 2. Environment Setup

```bash
# Set NACOS connection parameters
export NACOS_SERVER_ADDR=mse-9e4abfa0-nacos-ans.mse.aliyuncs.com:8848
export NACOS_NAMESPACE=8bc453ff-d8df-4ee3-acf4-4de86c19f955
export NACOS_ACCESS_KEY=your-access-key
export NACOS_SECRET_KEY=your-secret-key
```

### 3. Configuration Validation

```bash
# Validate NACOS configuration
npm run nacos:validate

# Test NACOS connection
npm run nacos:test
```

## 🔧 Configuration Management

### Manual Configuration via Aliyun Portal

Configuration is managed manually through the Aliyun MSE NACOS console:

1. **Access Aliyun MSE Console**: Log into your Aliyun account
2. **Navigate to NACOS**: Go to MSE → NACOS → Configuration Management
3. **Create/Edit Configuration**:
   - **Data ID**: `mcp-server-odi`
   - **Group**: `DEFAULT_GROUP`
   - **Namespace**: `8bc453ff-d8df-4ee3-acf4-4de86c19f955`

### Sample Configuration Structure

```json
{
  "VITE_API_HOST": "https://admin.ingka-dt.cn/app-api/orders-portal",
  "VITE_API_HOST_KONG": "https://mpp-fe-i.ingka-internal.cn/order-web",
  "VITE_MASTER_DATA_API_HOST": "https://admin.ingka-dt.cn/master-data",
  "VITE_MASTER_DATA_API_KEY": "your-api-key",
  "X_CUSTOM_REFERRER": "https://admin.ingka-dt.cn/app/odi/oms/index",

  "AUTH_COOKIES": "orders-portal=your-auth-cookies",

  "OAUTH2_ENABLED": false,
  "OAUTH2_CLIENT_ID": "mcp-server-odi",
  "KEYCLOAK_BASE_URL": "https://keycloak.ingka-dt.cn",

  "DEBUG_SERVICE_ADAPTER": false,
  "CONNECTION_AUTH_ENABLED": true,
  "CONNECTION_AUTH_STRICT": true,
  "CONNECTION_AUTH_TEST_API": false,

  "FEATURES": {
    "ENABLE_ADVANCED_LOGGING": true,
    "ENABLE_METRICS_COLLECTION": true,
    "ENABLE_PERFORMANCE_MONITORING": true
  }
}
```

### Environment-Specific Configuration

You can create different configurations for different environments by using different Data IDs or namespaces in the Aliyun portal.

### Available Scripts

| Script | Purpose |
|--------|---------|
| `npm run nacos:validate` | Validate NACOS configuration |
| `npm run nacos:test` | Test NACOS connection |
| `node scripts/nacos-config-manager.js help` | Show detailed help |

## 🔒 Security

### Credential Management

- **MSE Access Keys**: Stored in Kubernetes secrets (`mse-access-id-order`, `mse-access-secret-order`)
- **API Keys**: Stored in NACOS configuration (encrypted at rest)
- **Auth Cookies**: Stored in NACOS configuration (encrypted at rest)

### Best Practices

1. **Use environment-specific configurations** for different deployment stages
2. **Rotate credentials regularly** in both Kubernetes secrets and NACOS
3. **Monitor configuration changes** through NACOS audit logs
4. **Test configuration changes** in lower environments first

## 🧪 Testing

### NACOS Integration Tests

The blackbox test suite includes NACOS integration tests:

```bash
# Run NACOS integration tests
make test-full

# Run specific NACOS test
node blackbox/tests/nacos-integration-test.js
```

### Test Coverage

- ✅ NACOS client initialization
- ✅ Configuration loading from NACOS
- ✅ Environment variable fallback
- ✅ Configuration endpoint validation
- ✅ Dynamic configuration updates (optional)

## 🔍 Troubleshooting

### Common Issues

1. **Connection Failed**
   ```
   Error: Failed to initialize NACOS client
   ```
   - Check NACOS server address and credentials
   - Verify network connectivity to MSE

2. **Configuration Not Found**
   ```
   Warning: No configuration found in NACOS for mcp-server-odi
   ```
   - Upload configuration: `npm run nacos:upload`
   - Check dataId and group settings

3. **Invalid JSON Configuration**
   ```
   Error: Failed to parse configuration from NACOS
   ```
   - Validate JSON syntax in NACOS console
   - Re-upload valid configuration

### Debug Commands

```bash
# Check NACOS connectivity
curl -X GET "http://mse-9e4abfa0-nacos-ans.mse.aliyuncs.com:8848/nacos/v1/cs/configs?dataId=mcp-server-odi&group=DEFAULT_GROUP"

# Validate local configuration
node -e "console.log(JSON.parse(require('fs').readFileSync('config/nacos-config-template.json')))"

# Test NACOS client
node -e "import('./src/config/nacos.js').then(m => m.default.loadConfig().then(console.log))"
```

## 📚 Related Documentation

- [Environment Configuration Guide](./ENVIRONMENT_CONFIGURATION.md)
- [Deployment Guide](../DEPLOYMENT.md)
- [NACOS Blackbox Testing](./NACOS_BLACKBOX_TESTING.md)
- [Security Best Practices](./SECURITY_BEST_PRACTICES.md)

## 🔗 External Resources

- [Alibaba Cloud MSE Documentation](https://www.alibabacloud.com/help/en/mse)
- [NACOS Configuration Management](https://nacos.io/en-us/docs/configuration-management.html)
- [Node.js NACOS Client](https://github.com/nacos-group/nacos-sdk-nodejs)
