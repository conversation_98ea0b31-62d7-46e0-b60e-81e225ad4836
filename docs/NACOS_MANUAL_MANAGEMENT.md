# 🔧 NACOS Manual Configuration Management

This guide explains how to manually manage NACOS configuration through the <PERSON><PERSON> portal for the MCP Server ODI.

## 📋 Overview

The MCP Server ODI integrates with NACOS for centralized configuration management, but configuration is managed manually through the Aliyun MSE portal rather than automated scripts. This provides better control and security for production environments.

## 🏗️ Configuration Setup

### 1. Access Aliyun MSE Console

1. Log into your Aliyun account
2. Navigate to **MSE (Microservices Engine)**
3. Go to **NACOS** → **Configuration Management**

### 2. Create Configuration

Create a new configuration with these settings:

- **Data ID**: `mcp-server-odi`
- **Group**: `DEFAULT_GROUP`
- **Namespace**: `8bc453ff-d8df-4ee3-acf4-4de86c19f955`
- **Configuration Format**: `JSON`

### 3. Configuration Content

Add your JSON configuration in the Aliyun portal:

```json
{
  "VITE_API_HOST": "https://admin.ingka-dt.cn/app-api/orders-portal",
  "VITE_API_HOST_KONG": "https://mpp-fe-i.ingka-internal.cn/order-web",
  "VITE_MASTER_DATA_API_HOST": "https://admin.ingka-dt.cn/master-data",
  "VITE_MASTER_DATA_API_KEY": "your-api-key",
  "X_CUSTOM_REFERRER": "https://admin.ingka-dt.cn/app/odi/oms/index",
  
  "AUTH_COOKIES": "orders-portal=your-auth-cookies",
  
  "OAUTH2_ENABLED": false,
  "OAUTH2_CLIENT_ID": "mcp-server-odi",
  "KEYCLOAK_BASE_URL": "https://keycloak.ingka-dt.cn",
  "KEYCLOAK_REALM": "master",
  
  "DEBUG_SERVICE_ADAPTER": false,
  "CONNECTION_AUTH_ENABLED": true,
  "CONNECTION_AUTH_STRICT": true,
  "CONNECTION_AUTH_TEST_API": false,
  
  "FEATURES": {
    "ENABLE_ADVANCED_LOGGING": true,
    "ENABLE_METRICS_COLLECTION": true,
    "ENABLE_PERFORMANCE_MONITORING": true,
    "ENABLE_CACHE": true,
    "ENABLE_RATE_LIMITING": false
  }
}
```

## 🔍 Validation Tools

### Test NACOS Connection

```bash
# Set environment variables
export NACOS_SERVER_ADDR=mse-9e4abfa0-nacos-ans.mse.aliyuncs.com:8848
export NACOS_NAMESPACE=8bc453ff-d8df-4ee3-acf4-4de86c19f955
export NACOS_ACCESS_KEY=your-access-key
export NACOS_SECRET_KEY=your-secret-key

# Test connection
npm run nacos:test
```

### Validate Configuration

```bash
# Validate NACOS configuration
npm run nacos:validate
```

### Available Commands

| Command | Purpose |
|---------|---------|
| `npm run nacos:test` | Test connection to NACOS server |
| `npm run nacos:validate` | Validate configuration in NACOS |
| `node scripts/nacos-config-manager.js help` | Show help |

## 🔄 Configuration Fallback

The MCP server uses this configuration hierarchy:

1. **NACOS Configuration** (highest priority)
2. **Environment Variables** (including .env file)
3. **Default Values** (lowest priority)

If NACOS is unavailable, the server will automatically fall back to environment variables and continue running.

## 🔒 Security Best Practices

### 1. Access Control
- Use proper IAM roles for NACOS access
- Limit configuration editing permissions
- Enable audit logging in Aliyun

### 2. Sensitive Data
- Store API keys and secrets in NACOS (encrypted at rest)
- Use different configurations for different environments
- Regularly rotate credentials

### 3. Change Management
- Test configuration changes in lower environments first
- Use NACOS versioning features
- Document configuration changes

## 🛠️ Environment-Specific Configurations

### Development Environment
```json
{
  "VITE_API_HOST": "https://fe-dev-i.ingka-dt.cn",
  "DEBUG_SERVICE_ADAPTER": true,
  "CONNECTION_AUTH_ENABLED": false,
  "OAUTH2_ENABLED": false
}
```

### Production Environment
```json
{
  "VITE_API_HOST": "https://admin.ingka-internal.cn/app-api/orders-portal",
  "DEBUG_SERVICE_ADAPTER": false,
  "CONNECTION_AUTH_ENABLED": true,
  "CONNECTION_AUTH_STRICT": true,
  "OAUTH2_ENABLED": true
}
```

## 🔍 Troubleshooting

### Common Issues

1. **Configuration Not Loading**
   - Check Data ID, Group, and Namespace settings
   - Verify NACOS credentials
   - Test connection with `npm run nacos:test`

2. **Access Denied**
   - Verify NACOS access key and secret
   - Check IAM permissions in Aliyun
   - Ensure namespace access is granted

3. **Invalid JSON**
   - Validate JSON syntax in the Aliyun portal
   - Check for trailing commas or syntax errors
   - Use JSON validation tools

### Debug Commands

```bash
# Test NACOS connection
npm run nacos:test

# Validate configuration
npm run nacos:validate

# Check server logs for NACOS messages
npm start
# Look for: "NACOS configuration not available" or "Configuration loaded from NACOS"
```

## 📚 Related Documentation

- [NACOS Integration Guide](./NACOS_INTEGRATION_GUIDE.md)
- [Environment Configuration](./ENVIRONMENT_CONFIGURATION.md)
- [Deployment Guide](../DEPLOYMENT.md)
- [Security Best Practices](./SECURITY_BEST_PRACTICES.md)

## 🔗 External Resources

- [Aliyun MSE Documentation](https://www.alibabacloud.com/help/en/mse)
- [NACOS Configuration Management](https://nacos.io/en-us/docs/configuration-management.html)
