# 🔧 NACOS Integration for Blackbox Testing

This document explains how NACOS/MSE configuration management has been integrated into the blackbox testing suite for the MCP Server ODI.

## 📋 Overview

The blackbox testing environment now supports NACOS (Alibaba Cloud MSE) configuration management, following the same enterprise pattern used by production services like `master-data`, `invoice-gateway-service`, and other Java microservices.

## 🏗️ Architecture

### Configuration Hierarchy

```
Production Services (Java)     →    MCP Server (Node.js)    →    Blackbox Tests
├── values.yaml                     ├── values.yaml              ├── docker-compose.test.yml
├── MSE secrets                     ├── MSE secrets              ├── .env.test
└── NACOS config store             └── NACOS config store       └── .env.test.local
```

### Environment Variable Pattern

All environments follow the same MSE/NACOS pattern:

```yaml
# MSE Access Credentials (from Kubernetes secrets)
- name: MSE_ACCESS_KEY
  valueFrom:
    secretKeyRef:
      name: mse-access-id-order
      key: key
- name: MSE_ACCESS_SECRET
  valueFrom:
    secretKeyRef:
      name: mse-access-secret-order
      key: key

# NACOS Configuration
- name: NACOS_SERVER_ADDR
  value: mse-9e4abfa0-nacos-ans.mse.aliyuncs.com:8848
- name: NACOS_NAMESPACE
  value: 8bc453ff-d8df-4ee3-acf4-4de86c19f955
- name: NACOS_ACCESS_KEY
  value: $(MSE_ACCESS_KEY)
- name: NACOS_SECRET_KEY
  value: $(MSE_ACCESS_SECRET)
```

## 🚀 Quick Start

### 1. Setup Test Environment

```bash
# Setup NACOS test configuration
make setup-nacos-test

# Edit with actual test credentials
nano blackbox/config/.env.test.local

# Validate configuration
make validate-nacos-test
```

### 2. Run Tests

```bash
# Quick test with NACOS integration
make test-fast

# Complete test suite
make test-full
```

## 📁 File Structure

```
blackbox/
├── config/
│   ├── docker-compose.test.yml     # Docker Compose with NACOS env vars
│   ├── .env.test                   # Template configuration
│   └── .env.test.local            # Local overrides (gitignored)
├── scripts/
│   └── setup-nacos-test.sh        # NACOS setup and validation script
└── tests/
    └── docker-test-runner.js       # Test orchestrator with NACOS support
```

## 🔧 Configuration Details

### Environment Variables

| Variable | Purpose | Example |
|----------|---------|---------|
| `MSE_ACCESS_KEY_TEST` | MSE access key for test environment | `test-access-key` |
| `MSE_ACCESS_SECRET_TEST` | MSE access secret for test environment | `test-access-secret` |
| `NACOS_SERVER_ADDR_TEST` | NACOS server address | `mse-9e4abfa0-nacos-ans.mse.aliyuncs.com:8848` |
| `NACOS_NAMESPACE_TEST` | NACOS namespace ID | `8bc453ff-d8df-4ee3-acf4-4de86c19f955` |
| `VITE_API_HOST_TEST` | Test API host | `https://fe-dev-i.ingka-dt.cn` |
| `VITE_MASTER_DATA_API_KEY_TEST` | Test master data API key | `test-api-key` |

### Docker Services

The test environment includes three services with NACOS configuration:

1. **`mcp-server-oauth2-disabled`** - OAuth2 disabled test server
2. **`mcp-server-oauth2-enabled`** - OAuth2 enabled test server  
3. **`test-runner`** - Test orchestrator with NACOS validation

## 🔒 Security

### Credential Management

- **Template**: `.env.test` contains safe default values
- **Local Overrides**: `.env.test.local` contains actual credentials (gitignored)
- **CI/CD**: Use environment variables or secrets management

### Best Practices

1. **Never commit** `.env.test.local` to version control
2. **Use test-specific** credentials, not production credentials
3. **Validate configuration** before running tests
4. **Rotate credentials** regularly

## 🛠️ Available Commands

### Make Commands

```bash
make setup-nacos-test     # Setup NACOS test environment
make validate-nacos-test  # Validate NACOS configuration
make test-fast           # Quick test with NACOS
make test-full           # Complete test suite with NACOS
make clean              # Clean up Docker resources
```

### Script Commands

```bash
./blackbox/scripts/setup-nacos-test.sh           # Setup
./blackbox/scripts/setup-nacos-test.sh --validate # Validate
./blackbox/scripts/setup-nacos-test.sh --help     # Help
```

## 🔍 Troubleshooting

### Common Issues

1. **Missing credentials**: Run `make setup-nacos-test` first
2. **Default values**: Update `.env.test.local` with actual credentials
3. **Permission denied**: Run `chmod +x blackbox/scripts/setup-nacos-test.sh`
4. **Docker issues**: Run `make clean` to cleanup resources

### Validation

```bash
# Check configuration
make validate-nacos-test

# Check Docker services
make status

# View logs
make logs
```

## 🔗 Integration with Production

The blackbox testing NACOS configuration mirrors the production deployment pattern:

- **Same namespace**: `8bc453ff-d8df-4ee3-acf4-4de86c19f955`
- **Same server**: `mse-9e4abfa0-nacos-ans.mse.aliyuncs.com:8848`
- **Same credential pattern**: MSE secrets with NACOS variables
- **Same environment structure**: Consistent variable naming

This ensures that blackbox tests validate the same configuration management approach used in production.

## 📚 Related Documentation

- [Environment Configuration Guide](./ENVIRONMENT_CONFIGURATION.md)
- [Blackbox Testing Guide](../blackbox/README.md)
- [Deployment Guide](../DEPLOYMENT.md)
- [Security Best Practices](./SECURITY_BEST_PRACTICES.md)
