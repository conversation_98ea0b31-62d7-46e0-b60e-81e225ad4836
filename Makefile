.PHONY: help test-fast test-full clean setup-nacos-test validate-nacos-test

help: ## Show help
	@echo "🧪 MCP Server ODI - Integration Tests"
	@echo "====================================="
	@echo ""
	@echo "Integration & Blackbox Tests:"
	@echo "  test-fast    Fast blackbox test (~20s, recommended for dev)"
	@echo "  test-full    Complete blackbox test suite (~2-5min, for CI/CD)"
	@echo "  clean        Clean up Docker resources"
	@echo ""
	@echo "NACOS Test Configuration:"
	@echo "  setup-nacos-test     Setup NACOS test environment configuration"
	@echo "  validate-nacos-test  Validate NACOS test configuration"
	@echo ""
	@echo "Unit Tests:"
	@echo "  npm test     Show testing guidance"
	@echo "  npm run test:unit    Run unit tests (when implemented)"

# NACOS test configuration setup
setup-nacos-test: ## Setup NACOS test environment configuration
	@echo "🔧 Setting up NACOS test configuration..."
	@./blackbox/scripts/setup-nacos-test.sh

validate-nacos-test: ## Validate NACOS test configuration
	@echo "🔍 Validating NACOS test configuration..."
	@./blackbox/scripts/setup-nacos-test.sh --validate

# Fast test - just run the working MCP client test
test-fast: ## Run fast MCP client test
	@echo "⚡ Running fast blackbox test..."
	@if [ -f blackbox/config/.env.test.local ]; then \
		echo "📋 Loading test environment from .env.test.local"; \
		export $$(grep -v '^#' blackbox/config/.env.test.local | xargs); \
	fi
	@docker-compose -f blackbox/config/docker-compose.test.yml up mcp-server-oauth2-disabled --detach
	@sleep 10
	@MCP_SERVER_URL=http://localhost:3000 node blackbox/tests/mcp-client-simulator.js
	@docker-compose -f blackbox/config/docker-compose.test.yml down

# Full test suite using Docker Compose
test-full: ## Run full Docker test suite
	@echo "🧪 Running full test suite..."
	@if [ -f blackbox/config/.env.test.local ]; then \
		echo "📋 Loading test environment from .env.test.local"; \
		export $$(grep -v '^#' blackbox/config/.env.test.local | xargs); \
	fi
	@docker-compose -f blackbox/config/docker-compose.test.yml up --build --abort-on-container-exit
	@docker-compose -f blackbox/config/docker-compose.test.yml down -v --remove-orphans

# Clean up Docker resources
clean: ## Clean up Docker resources
	@echo "🧹 Cleaning up..."
	@docker-compose -f blackbox/config/docker-compose.test.yml down -v --remove-orphans 2>/dev/null || true
	@docker network rm mcp-test-network 2>/dev/null || true
	@docker system prune -f --volumes 2>/dev/null || true

# Development utilities
logs: ## Show Docker service logs
	@docker-compose -f blackbox/config/docker-compose.test.yml logs

status: ## Show Docker service status
	@docker-compose -f blackbox/config/docker-compose.test.yml ps

summary: ## Show available commands
	@echo "📊 MCP Server ODI - Available Commands"
	@echo "====================================="
	@echo ""
	@echo "Integration Tests:"
	@echo "  make test-fast    # Quick blackbox test (~20s)"
	@echo "  make test-full    # Complete test suite (~2-5min)"
	@echo "  make clean        # Clean up Docker resources"
	@echo ""
	@echo "NACOS Test Configuration:"
	@echo "  make setup-nacos-test     # Setup NACOS test environment"
	@echo "  make validate-nacos-test  # Validate NACOS test config"
	@echo ""
	@echo "Development:"
	@echo "  make logs         # Show service logs"
	@echo "  make status       # Show service status"
	@echo "  make help         # Show this help"
	@echo ""
	@echo "Unit Tests:"
	@echo "  npm test          # Show testing guidance"
	@echo "  npm run test:unit # Run unit tests"
